import React from 'react';
import { Shield, Leaf, Droplet, Heart, Sun } from 'lucide-react';

const Benefits = () => {
  const benefits = [
    {
      icon: <Shield className="h-10 w-10 text-green" />,
      title: "Boosts Immunity",
      description: "Rich in essential nutrients that strengthen your body's natural defense system."
    },
    {
      icon: <Leaf className="h-10 w-10 text-green" />,
      title: "Cold-Pressed, Chemical-Free",
      description: "Extracted without heat or chemicals, preserving all natural nutrients and flavor."
    },
    {
      icon: <Sun className="h-10 w-10 text-green" />,
      title: "Traditional Kolhu Method",
      description: "Crafted using centuries-old wooden press techniques for authentic taste."
    },
    {
      icon: <Heart className="h-10 w-10 text-green" />,
      title: "Heart Healthy",
      description: "Contains the perfect balance of fatty acids to support cardiovascular health."
    },
    {
      icon: <Droplet className="h-10 w-10 text-green" />,
      title: "Versatile Usage",
      description: "Ideal for cooking authentic Indian dishes and traditional body massage."
    }
  ];

  return (
    <section id="benefits" className="section-padding bg-cream">
      <div className="container-custom">
        <h2 className="section-title">Natural Benefits</h2>
        <p className="section-subtitle">
          Discover the many ways our pure mustard oil enhances your health and wellbeing,
          just as nature intended.
        </p>

        <div className="space-y-12">
          {benefits.map((benefit, index) => (
            <div
              key={index}
              className={`flex items-center gap-8 ${
                index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'
              } group`}
            >
              {/* Icon Circle */}
              <div className="flex-shrink-0 w-24 h-24 bg-gradient-to-br from-green to-green-600 rounded-full flex items-center justify-center shadow-xl group-hover:scale-110 transition-transform duration-300">
                <div className="text-white group-hover:scale-110 transition-transform duration-300">
                  {benefit.icon}
                </div>
              </div>

              {/* Content */}
              <div className="flex-1 space-y-3">
                <h3 className="text-2xl font-bold text-brown group-hover:text-green transition-colors duration-300">
                  {benefit.title}
                </h3>
                <p className="text-lg text-brown/80 leading-relaxed">
                  {benefit.description}
                </p>
              </div>

              {/* Floating Decorative Elements */}
              <div className="absolute opacity-20 group-hover:opacity-40 transition-opacity duration-300">
                <div className="w-3 h-3 bg-mustard rounded-full animate-pulse"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Benefits;