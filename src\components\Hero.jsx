import React from 'react';
import { ChevronDown } from 'lucide-react';
import { Link } from 'react-router-dom';
import CSS3DBottle from './CSS3DBottle';
import bottleImage from '../assets/WhatsApp Image 2025-05-28 at 11.47.00_d06dfd50.jpg';

const Hero = () => {
  return (
    <section
      id="home"
      className="relative min-h-screen flex items-center bg-mustard-field bg-cover bg-center overflow-hidden"
    >
      <div className="absolute inset-0 bg-brown/30 z-0"></div>

      {/* Floating Village Elements */}
      <div className="absolute top-20 left-10 w-4 h-4 bg-mustard/30 rounded-full village-float"></div>
      <div className="absolute top-40 right-20 w-3 h-3 bg-green/30 rounded-full village-pulse"></div>
      <div className="absolute bottom-40 left-20 w-2 h-2 bg-white/30 rounded-full village-bounce"></div>
      <div className="absolute top-60 left-1/3 w-5 h-5 bg-mustard/20 rounded-full village-float animation-delay-150"></div>
      <div className="absolute bottom-60 right-1/3 w-3 h-3 bg-green/20 rounded-full village-pulse animation-delay-300"></div>

      <div className="container-custom relative z-10 flex flex-col md:flex-row items-center">
        <div className="w-full md:w-1/2 text-white mb-12 md:mb-0">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-4 leading-tight">
            <span className="block text-white drop-shadow-lg">
              Vastavik Mustard Oil
            </span>
            <span className="block text-mustard italic mt-2 text-3xl md:text-4xl font-medium drop-shadow-md">
              शुद्धता जो गांव से आयी है
            </span>
          </h1>

          <p className="text-lg md:text-xl mb-8 max-w-lg">
            Experience the authentic taste of tradition with our pure,
            cold-pressed mustard oil crafted using centuries-old village methods.
          </p>

          <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-6">
            <Link to="/products" className="inline-flex items-center justify-center px-8 py-4 bg-mustard text-brown font-bold rounded-full hover:bg-mustard/90 transform hover:-translate-y-1 hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">
              Shop Now
            </Link>
            <Link to="/about" className="inline-flex items-center justify-center px-8 py-4 bg-transparent border-2 border-white text-white font-semibold rounded-full hover:bg-white hover:text-brown transition-all duration-300 transform hover:-translate-y-1 hover:scale-105 shadow-lg hover:shadow-xl">
              Our Story
            </Link>
          </div>
        </div>

        <div className="w-full md:w-1/2 flex justify-center md:justify-end">
          <div className="relative group">
            {/* Bottle Shadow */}
            <div className="absolute top-8 left-8 w-96 h-96 bg-brown/20 rounded-lg transform rotate-6 blur-sm"></div>

            <img
              src={bottleImage}
              alt="Vastavik Mustard Oil Bottle"
              className="h-96 object-contain rounded-lg transform rotate-6 shadow-2xl group-hover:rotate-0 transition-transform duration-500 village-glow"
            />

            <div className="absolute -bottom-4 -right-4 bg-mustard text-brown px-4 py-2 rounded-md shadow-lg village-pulse">
              <span className="text-sm font-medium">100% Pure</span>
            </div>

            {/* Floating Particles around bottle */}
            <div className="absolute -top-4 -right-4 w-2 h-2 bg-mustard rounded-full village-float"></div>
            <div className="absolute -bottom-4 -left-4 w-1 h-1 bg-green rounded-full village-pulse"></div>
            <div className="absolute top-1/2 -left-6 w-1 h-1 bg-white rounded-full village-bounce"></div>
          </div>
        </div>
      </div>

      {/* Removed all overlay cards for cleaner look */}

      <div className="absolute bottom-8 left-1/2 -translate-x-1/2 animate-bounce">
        <ChevronDown className="h-8 w-8 text-white drop-shadow-lg" />
      </div>

      {/* 3D Scrolling Oil Bottle */}
      <CSS3DBottle />
    </section>
  );
};

export default Hero;