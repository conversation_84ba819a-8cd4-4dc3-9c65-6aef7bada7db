import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { ChevronDown, Play, Volume2, VolumeX } from 'lucide-react';
import VillageScene3D from './VillageScene3D';
import Village3DFallback from './Village3DFallback';

const Hero3D = () => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [showTraditional, setShowTraditional] = useState(false);
  const [show3D, setShow3D] = useState(false); // Start with fallback
  const [soundEnabled, setSoundEnabled] = useState(false);

  useEffect(() => {
    // Quick loading for better UX
    const timer = setTimeout(() => {
      setIsLoaded(true);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  const toggleView = () => {
    setShowTraditional(!showTraditional);
  };

  const toggleSound = () => {
    setSoundEnabled(!soundEnabled);
    // Here you can add ambient village sounds
  };

  if (!isLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-mustard/20 via-cream to-green/20">
        <div className="text-center">
          <div className="relative w-32 h-32 mx-auto mb-8">
            <div className="absolute inset-0 border-4 border-mustard/30 rounded-full"></div>
            <div className="absolute inset-0 border-4 border-mustard border-t-transparent rounded-full animate-spin"></div>
            <div className="absolute inset-4 border-4 border-green/30 rounded-full"></div>
            <div className="absolute inset-4 border-4 border-green border-t-transparent rounded-full animate-spin animation-delay-150"></div>
          </div>
          <h2 className="text-2xl font-bold text-brown mb-4">गांव से आ रहा है...</h2>
          <p className="text-brown/70">Loading Village Experience</p>
        </div>
      </div>
    );
  }

  return (
    <section className="relative min-h-screen overflow-hidden">
      {/* Different Hero Versions */}
      {showTraditional ? (
        // Traditional Hero Section
        <div
          className="relative min-h-screen flex items-center bg-mustard-field bg-cover bg-center"
        >
          <div className="absolute inset-0 bg-brown/30 z-0"></div>

          <div className="container-custom relative z-10 flex flex-col md:flex-row items-center">
            <div className="w-full md:w-1/2 text-white mb-12 md:mb-0">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-4 leading-tight">
                <span className="block">Vastvik Mustard Oil</span>
                <span className="block text-mustard italic mt-2">
                  शुद्धता जो गांव से आयी है
                </span>
              </h1>

              <p className="text-lg md:text-xl mb-8 max-w-lg">
                Experience the authentic taste of tradition with our pure,
                cold-pressed mustard oil crafted using centuries-old village methods.
              </p>

              <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
                <Link to="/products" className="btn-primary text-center">
                  Shop Now
                </Link>
                <button
                  className="px-6 py-3 bg-transparent border-2 border-white text-white
                    font-semibold rounded-md hover:bg-white/20 transition-colors duration-300"
                >
                  <Play className="inline h-5 w-5 mr-2" />
                  Our Story
                </button>
              </div>
            </div>

            <div className="w-full md:w-1/2 flex justify-center md:justify-end">
              <div className="relative">
                <img
                  src="https://images.pexels.com/photos/4505168/pexels-photo-4505168.jpeg"
                  alt="Vastvik Mustard Oil Bottle"
                  className="h-96 object-contain rounded-lg transform rotate-6 shadow-2xl"
                />
                <div className="absolute -bottom-4 -right-4 bg-mustard text-brown px-4 py-2 rounded-md shadow-lg">
                  <span className="text-sm font-medium">100% Pure</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : show3D ? (
        // 3D Village Scene
        <VillageScene3D />
      ) : (
        // 3D Fallback Scene
        <Village3DFallback />
      )}

      {/* Control Panel */}
      <div className="absolute top-24 right-4 z-50 space-y-2">
        <button
          onClick={toggleView}
          className="w-12 h-12 bg-white/90 hover:bg-white rounded-full shadow-lg flex items-center justify-center transition-all duration-300 transform hover:scale-110"
          title={showTraditional ? "Switch to 3D View" : "Switch to Traditional View"}
        >
          {showTraditional ? (
            <div className="w-6 h-6 bg-gradient-to-br from-mustard to-green rounded"></div>
          ) : (
            <div className="w-6 h-6 bg-brown rounded"></div>
          )}
        </button>

        <button
          onClick={() => setShow3D(!show3D)}
          className="w-12 h-12 bg-white/90 hover:bg-white rounded-full shadow-lg flex items-center justify-center transition-all duration-300 transform hover:scale-110"
          title={show3D ? "Switch to Fallback" : "Switch to 3D"}
        >
          {show3D ? (
            <div className="w-6 h-6 bg-gradient-to-br from-blue-400 to-purple-500 rounded"></div>
          ) : (
            <div className="w-6 h-6 bg-gradient-to-br from-mustard to-green rounded"></div>
          )}
        </button>

        <button
          onClick={toggleSound}
          className="w-12 h-12 bg-white/90 hover:bg-white rounded-full shadow-lg flex items-center justify-center transition-all duration-300 transform hover:scale-110"
          title={soundEnabled ? "Mute Sounds" : "Enable Sounds"}
        >
          {soundEnabled ? (
            <Volume2 className="h-5 w-5 text-green" />
          ) : (
            <VolumeX className="h-5 w-5 text-brown" />
          )}
        </button>
      </div>

      {/* Floating Action Buttons */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-50">
        <div className="flex space-x-4">
          <Link
            to="/products"
            className="px-8 py-4 bg-mustard text-brown font-bold rounded-full shadow-xl hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 village-shimmer"
          >
            🛒 Shop Now
          </Link>

          <Link
            to="/about"
            className="px-8 py-4 bg-green text-white font-bold rounded-full shadow-xl hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 village-shimmer"
          >
            🏘️ Our Village
          </Link>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-50 animate-bounce">
        <ChevronDown className="h-8 w-8 text-white drop-shadow-lg" />
      </div>

      {/* Village Stats Overlay */}
      <div className="absolute bottom-20 right-8 z-50 bg-white/90 backdrop-blur-sm rounded-lg p-4 shadow-xl">
        <h3 className="font-bold text-brown mb-2">Village Heritage</h3>
        <div className="space-y-1 text-sm">
          <div className="flex justify-between">
            <span className="text-brown/70">Since:</span>
            <span className="font-semibold text-green">1950</span>
          </div>
          <div className="flex justify-between">
            <span className="text-brown/70">Families:</span>
            <span className="font-semibold text-green">100+</span>
          </div>
          <div className="flex justify-between">
            <span className="text-brown/70">Pure Oil:</span>
            <span className="font-semibold text-green">100%</span>
          </div>
        </div>
      </div>

      {/* Weather Widget */}
      <div className="absolute top-24 left-4 z-50 bg-white/90 backdrop-blur-sm rounded-lg p-4 shadow-xl">
        <div className="flex items-center space-x-2">
          <div className="text-2xl">☀️</div>
          <div>
            <div className="font-semibold text-brown">Punjab</div>
            <div className="text-sm text-brown/70">Perfect for Mustard</div>
          </div>
        </div>
      </div>

      {/* Interactive Hotspots (only in 3D mode) */}
      {!showTraditional && (
        <div className="absolute inset-0 z-40 pointer-events-none">
          {/* Hotspot 1 - Oil Bottle */}
          <div className="absolute top-1/2 left-1/3 transform -translate-x-1/2 -translate-y-1/2 pointer-events-auto">
            <div className="relative group">
              <div className="w-4 h-4 bg-mustard rounded-full animate-pulse cursor-pointer"></div>
              <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 bg-white rounded-lg p-3 shadow-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">
                <div className="text-sm font-semibold text-brown">Cold-Pressed Oil</div>
                <div className="text-xs text-brown/70">Traditional Kolhu Method</div>
              </div>
            </div>
          </div>

          {/* Hotspot 2 - Village House */}
          <div className="absolute top-2/3 right-1/3 transform translate-x-1/2 -translate-y-1/2 pointer-events-auto">
            <div className="relative group">
              <div className="w-4 h-4 bg-green rounded-full animate-pulse cursor-pointer"></div>
              <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 bg-white rounded-lg p-3 shadow-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">
                <div className="text-sm font-semibold text-brown">Village Home</div>
                <div className="text-xs text-brown/70">Where Tradition Lives</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Loading Overlay for 3D Scene */}
      {!showTraditional && (
        <div className="absolute top-4 left-1/2 transform -translate-x-1/2 z-50">
          <div className="bg-white/90 backdrop-blur-sm rounded-full px-4 py-2 shadow-lg">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-brown">3D Village Experience</span>
            </div>
          </div>
        </div>
      )}
    </section>
  );
};

export default Hero3D;
