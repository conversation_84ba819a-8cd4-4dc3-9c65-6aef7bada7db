{"name": "vastavik-mustard-oil-backend", "version": "1.0.0", "description": "Backend API for Vastavik Mustard Oil Website", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "seed": "node scripts/seedData.js"}, "keywords": ["nodejs", "express", "mongodb", "mustard-oil", "ecommerce", "api"], "author": "Vastavik Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "cloudinary": "^1.41.0", "nodemailer": "^6.9.7", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "express-validator": "^7.0.1", "compression": "^1.7.4", "morgan": "^1.10.0", "cookie-parser": "^1.4.6", "express-session": "^1.17.3", "connect-mongo": "^5.1.0", "stripe": "^14.7.0", "razorpay": "^2.9.2"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "@types/jest": "^29.5.8"}, "engines": {"node": ">=16.0.0"}}