import React from 'react';
import { Award, Clock, Sparkles } from 'lucide-react';

const WhyChooseUs = () => {
  const reasons = [
    {
      icon: <Sparkles className="h-16 w-16 text-mustard" />,
      title: "Purity",
      description: "Our mustard oil is 100% pure and unadulterated, extracted from the finest mustard seeds grown in fertile soils."
    },
    {
      icon: <Clock className="h-16 w-16 text-mustard" />,
      title: "Tradition",
      description: "We follow age-old extraction methods passed down through generations, preserving the authentic taste and benefits."
    },
    {
      icon: <Award className="h-16 w-16 text-mustard" />,
      title: "No Adulteration",
      description: "We take pride in delivering mustard oil that is free from any additives, preservatives, or chemical refinement."
    }
  ];

  return (
    <section className="py-20 bg-village-scene bg-cover bg-fixed relative">
      <div className="absolute inset-0 bg-brown/70"></div>
      
      <div className="container-custom relative z-10">
        <h2 className="text-3xl md:text-4xl font-bold text-white mb-6 text-center">
          Why Choose V<PERSON>vik?
        </h2>
        <p className="text-xl text-white/90 mb-12 text-center max-w-3xl mx-auto">
          Our commitment to quality and tradition sets us apart from commercial brands.
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {reasons.map((reason, index) => (
            <div 
              key={index} 
              className="bg-white/10 backdrop-blur-sm p-8 rounded-lg text-center 
              border border-white/20 hover:bg-white/20 transition-colors"
            >
              <div className="inline-block mb-6">
                {reason.icon}
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">
                {reason.title}
              </h3>
              <p className="text-white/90">
                {reason.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default WhyChooseUs;