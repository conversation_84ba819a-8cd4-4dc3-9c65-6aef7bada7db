import React, { useState } from 'react';
import { User, Package, Heart, Settings, LogOut, Eye, EyeOff } from 'lucide-react';

const Account = () => {
  const [activeTab, setActiveTab] = useState('profile');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isLogin, setIsLogin] = useState(true);
  
  const [loginData, setLoginData] = useState({
    email: '',
    password: ''
  });
  
  const [registerData, setRegisterData] = useState({
    name: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: ''
  });

  const handleLogin = (e) => {
    e.preventDefault();
    // Simulate login
    setIsLoggedIn(true);
  };

  const handleRegister = (e) => {
    e.preventDefault();
    // Simulate registration
    setIsLoggedIn(true);
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
    setActiveTab('profile');
  };

  const tabs = [
    { id: 'profile', label: 'Profile', icon: <User className="h-5 w-5" /> },
    { id: 'orders', label: 'Orders', icon: <Package className="h-5 w-5" /> },
    { id: 'wishlist', label: 'Wishlist', icon: <Heart className="h-5 w-5" /> },
    { id: 'settings', label: 'Settings', icon: <Settings className="h-5 w-5" /> }
  ];

  if (!isLoggedIn) {
    return (
      <div className="min-h-screen bg-cream pt-24">
        <div className="container-custom">
          <div className="max-w-md mx-auto">
            <div className="bg-white rounded-lg shadow-md p-8">
              <div className="text-center mb-8">
                <div className="w-16 h-16 bg-mustard/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <User className="h-8 w-8 text-green" />
                </div>
                <h1 className="text-2xl font-bold text-brown mb-2">
                  {isLogin ? 'Welcome Back' : 'Join Vastvik Family'}
                </h1>
                <p className="text-brown/70">
                  {isLogin ? 'Sign in to your account' : 'Create your account to get started'}
                </p>
              </div>

              {isLogin ? (
                <form onSubmit={handleLogin} className="space-y-6">
                  <div>
                    <label className="block text-brown font-medium mb-2">Email Address</label>
                    <input
                      type="email"
                      value={loginData.email}
                      onChange={(e) => setLoginData({...loginData, email: e.target.value})}
                      className="w-full px-4 py-3 border border-brown/20 rounded-md focus:outline-none focus:border-mustard"
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-brown font-medium mb-2">Password</label>
                    <div className="relative">
                      <input
                        type={showPassword ? 'text' : 'password'}
                        value={loginData.password}
                        onChange={(e) => setLoginData({...loginData, password: e.target.value})}
                        className="w-full px-4 py-3 border border-brown/20 rounded-md focus:outline-none focus:border-mustard pr-12"
                        placeholder="Enter your password"
                        required
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-brown/50"
                      >
                        {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                      </button>
                    </div>
                  </div>

                  <button type="submit" className="w-full btn-primary">
                    Sign In
                  </button>
                </form>
              ) : (
                <form onSubmit={handleRegister} className="space-y-6">
                  <div>
                    <label className="block text-brown font-medium mb-2">Full Name</label>
                    <input
                      type="text"
                      value={registerData.name}
                      onChange={(e) => setRegisterData({...registerData, name: e.target.value})}
                      className="w-full px-4 py-3 border border-brown/20 rounded-md focus:outline-none focus:border-mustard"
                      placeholder="Your full name"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-brown font-medium mb-2">Email Address</label>
                    <input
                      type="email"
                      value={registerData.email}
                      onChange={(e) => setRegisterData({...registerData, email: e.target.value})}
                      className="w-full px-4 py-3 border border-brown/20 rounded-md focus:outline-none focus:border-mustard"
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-brown font-medium mb-2">Phone Number</label>
                    <input
                      type="tel"
                      value={registerData.phone}
                      onChange={(e) => setRegisterData({...registerData, phone: e.target.value})}
                      className="w-full px-4 py-3 border border-brown/20 rounded-md focus:outline-none focus:border-mustard"
                      placeholder="+91 98765 43210"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-brown font-medium mb-2">Password</label>
                    <input
                      type="password"
                      value={registerData.password}
                      onChange={(e) => setRegisterData({...registerData, password: e.target.value})}
                      className="w-full px-4 py-3 border border-brown/20 rounded-md focus:outline-none focus:border-mustard"
                      placeholder="Create a password"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-brown font-medium mb-2">Confirm Password</label>
                    <input
                      type="password"
                      value={registerData.confirmPassword}
                      onChange={(e) => setRegisterData({...registerData, confirmPassword: e.target.value})}
                      className="w-full px-4 py-3 border border-brown/20 rounded-md focus:outline-none focus:border-mustard"
                      placeholder="Confirm your password"
                      required
                    />
                  </div>

                  <button type="submit" className="w-full btn-primary">
                    Create Account
                  </button>
                </form>
              )}

              <div className="mt-6 text-center">
                <p className="text-brown/70">
                  {isLogin ? "Don't have an account?" : "Already have an account?"}
                  <button
                    onClick={() => setIsLogin(!isLogin)}
                    className="ml-2 text-green hover:text-green-dark font-medium"
                  >
                    {isLogin ? 'Sign up' : 'Sign in'}
                  </button>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-cream pt-24">
      <div className="container-custom">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="text-center mb-6">
                <div className="w-20 h-20 bg-mustard/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <User className="h-10 w-10 text-green" />
                </div>
                <h2 className="text-xl font-semibold text-brown">John Doe</h2>
                <p className="text-brown/70"><EMAIL></p>
              </div>
              
              <nav className="space-y-2">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center space-x-3 px-4 py-3 rounded-md transition-colors ${
                      activeTab === tab.id
                        ? 'bg-mustard/20 text-green'
                        : 'text-brown hover:bg-cream'
                    }`}
                  >
                    {tab.icon}
                    <span>{tab.label}</span>
                  </button>
                ))}
                
                <button
                  onClick={handleLogout}
                  className="w-full flex items-center space-x-3 px-4 py-3 rounded-md text-red-600 hover:bg-red-50 transition-colors"
                >
                  <LogOut className="h-5 w-5" />
                  <span>Logout</span>
                </button>
              </nav>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-lg shadow-md p-8">
              {activeTab === 'profile' && (
                <div>
                  <h1 className="text-2xl font-bold text-brown mb-6">Profile Information</h1>
                  <form className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-brown font-medium mb-2">First Name</label>
                        <input
                          type="text"
                          defaultValue="John"
                          className="w-full px-4 py-3 border border-brown/20 rounded-md focus:outline-none focus:border-mustard"
                        />
                      </div>
                      <div>
                        <label className="block text-brown font-medium mb-2">Last Name</label>
                        <input
                          type="text"
                          defaultValue="Doe"
                          className="w-full px-4 py-3 border border-brown/20 rounded-md focus:outline-none focus:border-mustard"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-brown font-medium mb-2">Email Address</label>
                      <input
                        type="email"
                        defaultValue="<EMAIL>"
                        className="w-full px-4 py-3 border border-brown/20 rounded-md focus:outline-none focus:border-mustard"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-brown font-medium mb-2">Phone Number</label>
                      <input
                        type="tel"
                        defaultValue="+91 98765 43210"
                        className="w-full px-4 py-3 border border-brown/20 rounded-md focus:outline-none focus:border-mustard"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-brown font-medium mb-2">Address</label>
                      <textarea
                        rows={3}
                        defaultValue="123 Village Street, Punjab, India"
                        className="w-full px-4 py-3 border border-brown/20 rounded-md focus:outline-none focus:border-mustard"
                      ></textarea>
                    </div>
                    
                    <button type="submit" className="btn-primary">
                      Update Profile
                    </button>
                  </form>
                </div>
              )}

              {activeTab === 'orders' && (
                <div>
                  <h1 className="text-2xl font-bold text-brown mb-6">Order History</h1>
                  <div className="space-y-4">
                    <div className="border border-brown/20 rounded-lg p-6">
                      <div className="flex justify-between items-start mb-4">
                        <div>
                          <h3 className="font-semibold text-brown">Order #VAS001</h3>
                          <p className="text-brown/70">Placed on March 15, 2024</p>
                        </div>
                        <span className="px-3 py-1 bg-green/20 text-green rounded-full text-sm">Delivered</span>
                      </div>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span>Vastvik Cold-Pressed Mustard Oil (1L)</span>
                          <span>₹449</span>
                        </div>
                        <div className="flex justify-between font-semibold">
                          <span>Total</span>
                          <span>₹449</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'wishlist' && (
                <div>
                  <h1 className="text-2xl font-bold text-brown mb-6">My Wishlist</h1>
                  <p className="text-brown/70">Your wishlist is empty. Start adding products you love!</p>
                </div>
              )}

              {activeTab === 'settings' && (
                <div>
                  <h1 className="text-2xl font-bold text-brown mb-6">Account Settings</h1>
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-semibold text-brown mb-4">Notifications</h3>
                      <div className="space-y-3">
                        <label className="flex items-center">
                          <input type="checkbox" defaultChecked className="mr-3" />
                          <span className="text-brown">Email notifications for orders</span>
                        </label>
                        <label className="flex items-center">
                          <input type="checkbox" defaultChecked className="mr-3" />
                          <span className="text-brown">SMS notifications for delivery</span>
                        </label>
                        <label className="flex items-center">
                          <input type="checkbox" className="mr-3" />
                          <span className="text-brown">Marketing emails</span>
                        </label>
                      </div>
                    </div>
                    
                    <div>
                      <h3 className="text-lg font-semibold text-brown mb-4">Change Password</h3>
                      <div className="space-y-4 max-w-md">
                        <input
                          type="password"
                          placeholder="Current password"
                          className="w-full px-4 py-3 border border-brown/20 rounded-md focus:outline-none focus:border-mustard"
                        />
                        <input
                          type="password"
                          placeholder="New password"
                          className="w-full px-4 py-3 border border-brown/20 rounded-md focus:outline-none focus:border-mustard"
                        />
                        <input
                          type="password"
                          placeholder="Confirm new password"
                          className="w-full px-4 py-3 border border-brown/20 rounded-md focus:outline-none focus:border-mustard"
                        />
                        <button className="btn-secondary">Update Password</button>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Account;
