# 🌾 Vastavik Mustard Oil - Complete Setup Guide

## 🚀 Quick Start Guide

### 📋 Prerequisites
- Node.js (v16 or higher)
- MongoDB (local installation)
- Git
- Code editor (VS Code recommended)

### 🔧 Backend Setup

1. **Navigate to backend folder:**
```bash
cd backend
```

2. **Install dependencies:**
```bash
npm install
```

3. **Configure environment:**
```bash
# Copy the .env file and update with your settings
cp .env.example .env
```

4. **Update .env file with your configuration:**
```env
# Server Configuration
PORT=5000
NODE_ENV=development

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/vastavik_mustard_oil

# JWT Configuration
JWT_SECRET=vastavik_super_secret_key_2024
JWT_EXPIRE=30d

# Email Configuration (Gmail SMTP)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# Payment Configuration (Razorpay)
RAZORPAY_KEY_ID=rzp_test_your_key_id
RAZORPAY_KEY_SECRET=your_razorpay_secret

# Frontend URL
FRONTEND_URL=http://localhost:5174
```

5. **Start MongoDB:**
```bash
# On Windows
mongod

# On macOS (with Homebrew)
brew services start mongodb-community

# On Linux
sudo systemctl start mongod
```

6. **Seed sample data:**
```bash
npm run seed
```

7. **Start backend server:**
```bash
# Development mode
npm run dev

# Production mode
npm start
```

Backend will be running on: `http://localhost:5000`

### 🎨 Frontend Setup

1. **Navigate to frontend folder:**
```bash
cd ..  # Go back to root if in backend folder
```

2. **Install dependencies:**
```bash
npm install
```

3. **Configure environment:**
```bash
# Update .env file with your settings
```

4. **Update .env file:**
```env
# API Configuration
VITE_API_URL=http://localhost:5000/api/v1

# Admin WhatsApp Number (for notifications)
VITE_ADMIN_WHATSAPP=************

# Admin Email
VITE_ADMIN_EMAIL=<EMAIL>

# Payment Configuration
VITE_RAZORPAY_KEY_ID=rzp_test_your_key_id

# Features
VITE_ENABLE_NOTIFICATIONS=true
VITE_ENABLE_WHATSAPP_NOTIFICATIONS=true
VITE_ENABLE_EMAIL_NOTIFICATIONS=true
```

5. **Start frontend development server:**
```bash
npm run dev
```

Frontend will be running on: `http://localhost:5174`

## 🔐 Admin Access

### Default Admin Credentials:
- **Email:** <EMAIL>
- **Password:** admin123

### Admin Features:
- **Dashboard:** `http://localhost:5174/admin`
- **Product Management:** `http://localhost:5174/admin/products`
- **Order Notifications:** Real-time notifications via WhatsApp and email
- **User Management:** View and manage users
- **Analytics:** Sales and revenue tracking

## 📱 WhatsApp Integration

### Setup WhatsApp Notifications:

1. **Update admin WhatsApp number in .env:**
```env
VITE_ADMIN_WHATSAPP=************  # Replace with actual number
```

2. **Notification Types:**
- New order notifications
- Payment confirmations
- Cart additions (optional)
- Order status updates

3. **WhatsApp Message Format:**
```
🛒 *New Order Received!*

📋 Order: VMO20241201001
👤 Customer: John Doe
📱 Phone: +************
💰 Amount: ₹449
💳 Payment: COD
📍 Address: 123 Street, City

🔗 View Details: http://localhost:5174/admin/orders/...
```

## 📧 Email Notifications

### Setup Email Service:

1. **Gmail SMTP Configuration:**
```env
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password  # Generate from Google Account settings
```

2. **Email Templates Available:**
- Welcome emails
- Order confirmations
- Password reset
- Contact form responses
- Admin notifications

## 💳 Payment Integration

### Razorpay Setup:

1. **Create Razorpay Account:**
   - Go to https://razorpay.com
   - Create account and get API keys

2. **Update Configuration:**
```env
# Backend (.env)
RAZORPAY_KEY_ID=rzp_test_your_key_id
RAZORPAY_KEY_SECRET=your_razorpay_secret

# Frontend (.env)
VITE_RAZORPAY_KEY_ID=rzp_test_your_key_id
```

3. **Payment Methods Supported:**
- Credit/Debit Cards
- Net Banking
- UPI
- Wallets
- Cash on Delivery (COD)

## 📊 Features Overview

### 🔐 Authentication System:
- User registration with email verification
- Secure login with JWT tokens
- Password reset functionality
- Role-based access (Admin/User)

### 📦 Product Management:
- Admin can add/edit/delete products
- Stock management
- Price and discount management
- Image handling
- Category organization

### 🛒 Shopping Features:
- Add to cart functionality
- Cart management
- Coupon system
- Order creation
- Order tracking

### 📱 Admin Notifications:
- **Real-time notifications** when:
  - New order is placed
  - Payment is received/failed
  - User adds items to cart
  - Stock runs low

### 📈 Admin Dashboard:
- Sales analytics
- Revenue tracking
- User management
- Order management
- Product performance

## 🧪 Testing

### Test User Accounts:
- **User 1:** <EMAIL> / user123
- **User 2:** <EMAIL> / user123

### Sample Products:
- Vastavik Mustard Oil 500ml - ₹249 (₹199 discounted)
- Vastavik Mustard Oil 1L - ₹449 (₹399 discounted)
- Vastavik Mustard Oil 5L - ₹1999 (₹1799 discounted)

### API Testing:
```bash
# Health check
curl http://localhost:5000/health

# Get products
curl http://localhost:5000/api/v1/products

# Login
curl -X POST http://localhost:5000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'
```

## 🚀 Production Deployment

### Environment Variables for Production:
```env
NODE_ENV=production
MONGODB_URI=mongodb+srv://username:<EMAIL>/vastavik
JWT_SECRET=super_secure_production_secret
EMAIL_USER=<EMAIL>
RAZORPAY_KEY_ID=rzp_live_key
FRONTEND_URL=https://vastavik.com
```

### Build Commands:
```bash
# Frontend build
npm run build

# Backend with PM2
npm install -g pm2
pm2 start server.js --name "vastavik-api"
```

## 🆘 Troubleshooting

### Common Issues:

1. **MongoDB Connection Error:**
   - Ensure MongoDB is running
   - Check connection string in .env

2. **CORS Error:**
   - Verify FRONTEND_URL in backend .env
   - Check API_URL in frontend .env

3. **Email Not Sending:**
   - Verify Gmail app password
   - Check email configuration

4. **WhatsApp Not Working:**
   - Verify phone number format
   - Check if WhatsApp is installed

## 📞 Support

For issues or questions:
- **Email:** <EMAIL>
- **WhatsApp:** +************

---

**🌾 Happy Coding! Made with ❤️ for Vastavik Mustard Oil**
