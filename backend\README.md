# Vastavik Mustard Oil - Backend API

🌾 **Complete backend API for Vastavik Mustard Oil e-commerce website**

## 🚀 Features

### 🔐 Authentication & Authorization
- User registration with email verification
- Secure login with JWT tokens
- Password reset functionality
- Role-based access control (Admin, User)
- Account lockout after failed attempts
- Session management

### 📦 Product Management
- Complete product CRUD operations
- Advanced search and filtering
- Category-based organization
- Stock management
- Image handling
- SEO optimization
- Product reviews and ratings

### 🛒 Shopping Cart
- Add/remove items
- Quantity management
- Price calculations
- Coupon system
- Cart persistence
- Stock validation

### 📋 Order Management
- Order creation and tracking
- Multiple payment methods
- Order status updates
- Invoice generation
- Return/refund handling
- Order history

### 💳 Payment Integration
- Razorpay integration
- COD support
- Payment verification
- Webhook handling
- Transaction tracking

### 👥 User Management
- Profile management
- Address management
- Order history
- Wishlist functionality
- Account settings

### 📧 Email System
- Welcome emails
- Order confirmations
- Password reset
- Contact form responses
- Newsletter support

### 📊 Admin Dashboard
- Sales analytics
- User management
- Order management
- Product management
- Revenue tracking

## 🛠️ Technology Stack

- **Runtime**: Node.js
- **Framework**: Express.js
- **Database**: MongoDB with Mongoose
- **Authentication**: JWT + bcryptjs
- **Payment**: Razorpay
- **Email**: Nodemailer
- **Validation**: express-validator
- **Security**: Helmet, CORS, Rate limiting
- **File Upload**: Multer + Cloudinary

## 📁 Project Structure

```
backend/
├── models/           # Database models
│   ├── User.js
│   ├── Product.js
│   ├── Order.js
│   ├── Cart.js
│   └── Review.js
├── routes/           # API routes
│   ├── auth.js
│   ├── products.js
│   ├── orders.js
│   ├── cart.js
│   ├── users.js
│   ├── reviews.js
│   ├── contact.js
│   ├── payment.js
│   └── admin.js
├── middleware/       # Custom middleware
│   ├── auth.js
│   ├── errorHandler.js
│   └── notFound.js
├── utils/           # Utility functions
│   └── email.js
├── scripts/         # Database scripts
│   └── seedData.js
├── server.js        # Main server file
├── package.json
├── .env
└── README.md
```

## 🚀 Quick Start

### 1. Prerequisites
- Node.js (v16 or higher)
- MongoDB (local or cloud)
- Git

### 2. Installation

```bash
# Clone the repository
git clone <repository-url>
cd backend

# Install dependencies
npm install

# Create environment file
cp .env.example .env
```

### 3. Environment Configuration

Edit `.env` file with your settings:

```env
# Server
PORT=5000
NODE_ENV=development

# Database
MONGODB_URI=mongodb://localhost:27017/vastavik_mustard_oil

# JWT
JWT_SECRET=your_super_secret_key
JWT_EXPIRE=30d

# Email (Gmail SMTP)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# Payment (Razorpay)
RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_secret

# Frontend URL
FRONTEND_URL=http://localhost:5174
```

### 4. Database Setup

```bash
# Start MongoDB (if local)
mongod

# Seed sample data
npm run seed
```

### 5. Start Server

```bash
# Development mode
npm run dev

# Production mode
npm start
```

Server will start on `http://localhost:5000`

## 📚 API Documentation

### 🔐 Authentication Endpoints

```
POST /api/v1/auth/register     # Register new user
POST /api/v1/auth/login        # User login
POST /api/v1/auth/logout       # User logout
GET  /api/v1/auth/me           # Get current user
POST /api/v1/auth/verify-email # Verify email
POST /api/v1/auth/forgot-password # Forgot password
POST /api/v1/auth/reset-password   # Reset password
```

### 📦 Product Endpoints

```
GET    /api/v1/products              # Get all products
GET    /api/v1/products/:id          # Get single product
POST   /api/v1/products              # Create product (Admin)
PUT    /api/v1/products/:id          # Update product (Admin)
DELETE /api/v1/products/:id          # Delete product (Admin)
GET    /api/v1/products/featured/list    # Get featured products
GET    /api/v1/products/bestsellers/list # Get best sellers
```

### 🛒 Cart Endpoints

```
GET    /api/v1/cart           # Get user cart
POST   /api/v1/cart/add       # Add item to cart
PUT    /api/v1/cart/update    # Update cart item
DELETE /api/v1/cart/remove/:productId # Remove item
DELETE /api/v1/cart/clear     # Clear cart
POST   /api/v1/cart/coupon    # Apply coupon
```

### 📋 Order Endpoints

```
GET  /api/v1/orders           # Get user orders
GET  /api/v1/orders/:id       # Get single order
POST /api/v1/orders           # Create new order
PUT  /api/v1/orders/:id/cancel    # Cancel order
PUT  /api/v1/orders/:id/status    # Update status (Admin)
```

### 💳 Payment Endpoints

```
POST /api/v1/payment/create-order  # Create payment order
POST /api/v1/payment/verify       # Verify payment
POST /api/v1/payment/webhook      # Payment webhook
GET  /api/v1/payment/status/:orderId # Payment status
```

## 🔒 Security Features

- **JWT Authentication**: Secure token-based authentication
- **Password Hashing**: bcryptjs with salt rounds
- **Rate Limiting**: Prevent API abuse
- **CORS Protection**: Cross-origin request security
- **Helmet**: Security headers
- **Input Validation**: express-validator
- **Account Lockout**: Failed login attempt protection

## 📊 Sample Data

The seed script creates:

### 👤 Users
- **Admin**: <EMAIL> / admin123
- **Test User**: <EMAIL> / user123
- **Test User 2**: <EMAIL> / user123

### 📦 Products
- Vastavik Mustard Oil 500ml - ₹249
- Vastavik Mustard Oil 1L - ₹449  
- Vastavik Mustard Oil 5L - ₹1999

## 🧪 Testing

```bash
# Run tests
npm test

# Test specific endpoint
curl http://localhost:5000/api/v1/products
```

## 📈 Monitoring

### Health Check
```
GET /health
```

### API Status
```
GET /
```

## 🚀 Deployment

### Environment Variables for Production

```env
NODE_ENV=production
MONGODB_URI=mongodb+srv://username:<EMAIL>/vastavik
JWT_SECRET=super_secure_production_secret
EMAIL_USER=<EMAIL>
RAZORPAY_KEY_ID=rzp_live_key
FRONTEND_URL=https://vastavik.com
```

### PM2 Deployment

```bash
# Install PM2
npm install -g pm2

# Start with PM2
pm2 start server.js --name "vastavik-api"

# Monitor
pm2 monit
```

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📝 License

This project is licensed under the MIT License.

## 📞 Support

For support, email <EMAIL> or create an issue.

---

**🌾 Made with ❤️ for Vastavik Mustard Oil**
