import React, { useRef, useEffect, useState } from 'react';
import { Canvas, useFrame, useThree } from '@react-three/fiber';
import { ScrollControls, useScroll } from '@react-three/drei';
import * as THREE from 'three';

// 3D Oil Bottle Component
function OilBottle3D() {
  const bottleRef = useRef();
  const oilRef = useRef();
  const labelRef = useRef();
  const scroll = useScroll();

  useFrame((state) => {
    if (bottleRef.current && scroll) {
      const scrollY = scroll.offset;

      // Bottle rotation based on scroll
      bottleRef.current.rotation.y = scrollY * Math.PI * 2;
      bottleRef.current.rotation.x = Math.sin(scrollY * Math.PI) * 0.2;

      // Bottle position animation
      bottleRef.current.position.y = Math.sin(scrollY * Math.PI * 2) * 0.5;
      bottleRef.current.position.x = Math.cos(scrollY * Math.PI) * 0.3;

      // Oil level animation
      if (oilRef.current) {
        oilRef.current.scale.y = 0.8 + Math.sin(scrollY * Math.PI * 4) * 0.2;
      }

      // Label rotation
      if (labelRef.current) {
        labelRef.current.rotation.z = Math.sin(scrollY * Math.PI * 3) * 0.1;
      }
    }
  });

  return (
    <group ref={bottleRef} position={[0, 0, 0]}>
      {/* Bottle Body - Main Container */}
      <mesh position={[0, 0, 0]} castShadow receiveShadow>
        <cylinderGeometry args={[0.8, 1, 3, 16]} />
        <meshPhysicalMaterial
          color="#2B580C"
          transparent
          opacity={0.9}
          roughness={0.1}
          metalness={0.1}
          clearcoat={1}
          clearcoatRoughness={0.1}
        />
      </mesh>

      {/* Bottle Neck */}
      <mesh position={[0, 1.8, 0]} castShadow>
        <cylinderGeometry args={[0.3, 0.4, 0.6, 12]} />
        <meshPhysicalMaterial
          color="#2B580C"
          roughness={0.1}
          metalness={0.1}
        />
      </mesh>

      {/* Bottle Cap */}
      <mesh position={[0, 2.3, 0]} castShadow>
        <cylinderGeometry args={[0.35, 0.35, 0.3, 12]} />
        <meshPhysicalMaterial
          color="#E3B505"
          roughness={0.3}
          metalness={0.8}
        />
      </mesh>

      {/* Oil Inside Bottle */}
      <mesh ref={oilRef} position={[0, -0.5, 0]}>
        <cylinderGeometry args={[0.75, 0.95, 2.5, 16]} />
        <meshPhysicalMaterial
          color="#FFD700"
          transparent
          opacity={0.8}
          roughness={0}
          metalness={0}
          transmission={0.9}
          thickness={0.5}
        />
      </mesh>

      {/* Label */}
      <mesh ref={labelRef} position={[0, 0.3, 0.81]}>
        <planeGeometry args={[1.2, 1.6]} />
        <meshStandardMaterial color="#FFF8E8" />
      </mesh>

      {/* Label Text Background */}
      <mesh position={[0, 0.3, 0.82]}>
        <planeGeometry args={[1, 1.4]} />
        <meshStandardMaterial color="#FFFFFF" />
      </mesh>

      {/* Vastvik Text on Label */}
      <mesh position={[0, 0.7, 0.83]}>
        <planeGeometry args={[0.8, 0.2]} />
        <meshStandardMaterial color="#2B580C" />
      </mesh>

      {/* Pure Oil Text */}
      <mesh position={[0, 0.1, 0.83]}>
        <planeGeometry args={[0.6, 0.15]} />
        <meshStandardMaterial color="#E3B505" />
      </mesh>
    </group>
  );
}

// Floating Particles
function FloatingParticles() {
  const particlesRef = useRef();
  const scroll = useScroll();

  const particlePositions = React.useMemo(() => {
    const positions = new Float32Array(100 * 3);
    for (let i = 0; i < 100; i++) {
      positions[i * 3] = (Math.random() - 0.5) * 10;
      positions[i * 3 + 1] = (Math.random() - 0.5) * 10;
      positions[i * 3 + 2] = (Math.random() - 0.5) * 10;
    }
    return positions;
  }, []);

  useFrame((state) => {
    if (particlesRef.current && scroll) {
      particlesRef.current.rotation.y = scroll.offset * Math.PI;
      particlesRef.current.rotation.x = Math.sin(scroll.offset * Math.PI) * 0.2;
    }
  });

  return (
    <points ref={particlesRef}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={100}
          array={particlePositions}
          itemSize={3}
        />
      </bufferGeometry>
      <pointsMaterial size={0.05} color="#E3B505" transparent opacity={0.6} />
    </points>
  );
}

// Camera Controller
function CameraController() {
  const { camera } = useThree();
  const scroll = useScroll();

  useFrame(() => {
    if (scroll) {
      const scrollY = scroll.offset;

      // Camera movement based on scroll
      camera.position.x = Math.sin(scrollY * Math.PI) * 3;
      camera.position.y = 2 + Math.cos(scrollY * Math.PI * 2) * 1;
      camera.position.z = 5 + Math.sin(scrollY * Math.PI) * 2;

      // Always look at the bottle
      camera.lookAt(0, 0, 0);
    }
  });

  return null;
}

// Main 3D Scene
function BottleScene() {
  return (
    <>
      {/* Lighting Setup */}
      <ambientLight intensity={0.4} />
      <directionalLight
        position={[10, 10, 5]}
        intensity={1}
        castShadow
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
      />
      <pointLight position={[-10, -10, -10]} intensity={0.5} color="#E3B505" />
      <spotLight
        position={[0, 10, 0]}
        intensity={0.8}
        angle={0.3}
        penumbra={1}
        castShadow
      />

      {/* 3D Oil Bottle */}
      <OilBottle3D />

      {/* Floating Particles */}
      <FloatingParticles />

      {/* Camera Controller */}
      <CameraController />

      {/* Environment */}
      <mesh position={[0, -3, 0]} rotation={[-Math.PI / 2, 0, 0]} receiveShadow>
        <planeGeometry args={[20, 20]} />
        <meshStandardMaterial color="#90EE90" transparent opacity={0.3} />
      </mesh>
    </>
  );
}

// Main Component
const ScrollingOilBottle3D = () => {
  const [scrollY, setScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div className="fixed top-0 right-0 w-1/2 h-screen pointer-events-none z-10">
      <Canvas
        camera={{ position: [3, 2, 5], fov: 50 }}
        shadows
        className="w-full h-full"
      >
        <ScrollControls pages={3} damping={0.1}>
          <BottleScene />
        </ScrollControls>
      </Canvas>

      {/* Scroll Progress Indicator */}
      <div className="absolute bottom-8 right-8 bg-white/90 backdrop-blur-sm rounded-lg p-4 shadow-xl pointer-events-auto">
        <div className="text-center">
          <div className="text-sm font-medium text-brown mb-2">3D Bottle Animation</div>
          <div className="w-16 h-2 bg-cream rounded-full overflow-hidden">
            <div
              className="h-full bg-gradient-to-r from-mustard to-green transition-all duration-300"
              style={{ width: `${Math.min((scrollY / (document.body.scrollHeight - window.innerHeight)) * 100, 100)}%` }}
            ></div>
          </div>
          <div className="text-xs text-brown/70 mt-1">Scroll to animate</div>
        </div>
      </div>

      {/* 3D Controls Info */}
      <div className="absolute top-8 right-8 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-xl pointer-events-auto">
        <div className="text-xs text-brown">
          <div className="font-semibold mb-1">🎮 3D Controls</div>
          <div>📜 Scroll to rotate bottle</div>
          <div>🌊 Oil level animates</div>
          <div>📷 Camera follows scroll</div>
        </div>
      </div>
    </div>
  );
};

export default ScrollingOilBottle3D;
