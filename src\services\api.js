import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:5000/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error.response?.data || error.message);
  }
);

// Auth API
export const authAPI = {
  register: (userData) => api.post('/auth/register', userData),
  login: (credentials) => api.post('/auth/login', credentials),
  logout: () => api.post('/auth/logout'),
  getMe: () => api.get('/auth/me'),
  verifyEmail: (token) => api.post('/auth/verify-email', { token }),
  forgotPassword: (email) => api.post('/auth/forgot-password', { email }),
  resetPassword: (token, password) => api.post('/auth/reset-password', { token, password }),
};

// Products API
export const productsAPI = {
  getAll: (params = {}) => api.get('/products', { params }),
  getById: (id) => api.get(`/products/${id}`),
  create: (productData) => api.post('/products', productData),
  update: (id, productData) => api.put(`/products/${id}`, productData),
  delete: (id) => api.delete(`/products/${id}`),
  getFeatured: (limit = 6) => api.get(`/products/featured/list?limit=${limit}`),
  getBestSellers: (limit = 6) => api.get(`/products/bestsellers/list?limit=${limit}`),
};

// Cart API
export const cartAPI = {
  get: () => api.get('/cart'),
  add: (productId, quantity) => api.post('/cart/add', { productId, quantity }),
  update: (productId, quantity) => api.put('/cart/update', { productId, quantity }),
  remove: (productId) => api.delete(`/cart/remove/${productId}`),
  clear: () => api.delete('/cart/clear'),
  applyCoupon: (couponCode) => api.post('/cart/coupon', { couponCode }),
};

// Orders API
export const ordersAPI = {
  getAll: (params = {}) => api.get('/orders', { params }),
  getById: (id) => api.get(`/orders/${id}`),
  create: (orderData) => api.post('/orders', orderData),
  cancel: (id) => api.put(`/orders/${id}/cancel`),
  updateStatus: (id, status, message) => api.put(`/orders/${id}/status`, { status, message }),
};

// Payment API
export const paymentAPI = {
  createOrder: (orderId, amount) => api.post('/payment/create-order', { orderId, amount }),
  verify: (paymentData) => api.post('/payment/verify', paymentData),
  getStatus: (orderId) => api.get(`/payment/status/${orderId}`),
};

// Users API
export const usersAPI = {
  getProfile: () => api.get('/users/profile'),
  updateProfile: (userData) => api.put('/users/profile', userData),
  changePassword: (passwordData) => api.put('/users/password', passwordData),
  deleteAccount: (password, confirmation) => api.delete('/users/account', { 
    data: { password, confirmation } 
  }),
};

// Reviews API
export const reviewsAPI = {
  getProductReviews: (productId, params = {}) => api.get(`/reviews/product/${productId}`, { params }),
  create: (reviewData) => api.post('/reviews', reviewData),
  update: (id, reviewData) => api.put(`/reviews/${id}`, reviewData),
  delete: (id) => api.delete(`/reviews/${id}`),
  markHelpful: (id) => api.post(`/reviews/${id}/helpful`),
  report: (id, reason, description) => api.post(`/reviews/${id}/report`, { reason, description }),
};

// Contact API
export const contactAPI = {
  sendMessage: (messageData) => api.post('/contact/message', messageData),
  getMessages: (params = {}) => api.get('/contact/messages', { params }),
  getMessage: (id) => api.get(`/contact/messages/${id}`),
  updateStatus: (id, status, notes) => api.put(`/contact/messages/${id}/status`, { status, notes }),
  reply: (id, replyMessage) => api.post(`/contact/messages/${id}/reply`, { replyMessage }),
};

// Admin API
export const adminAPI = {
  getDashboard: (period = '30') => api.get(`/admin/dashboard?period=${period}`),
  getUsers: (params = {}) => api.get('/admin/users', { params }),
  getOrders: (params = {}) => api.get('/admin/orders', { params }),
  getAnalytics: (period = '30') => api.get(`/admin/analytics?period=${period}`),
};

// WhatsApp API
export const whatsappAPI = {
  sendOrderNotification: (orderData) => {
    const message = `🛒 *New Order Received!*\n\n` +
      `📋 Order: ${orderData.orderNumber}\n` +
      `👤 Customer: ${orderData.customerName}\n` +
      `📱 Phone: ${orderData.phone}\n` +
      `💰 Amount: ₹${orderData.total}\n` +
      `💳 Payment: ${orderData.paymentMethod}\n` +
      `📍 Address: ${orderData.address}\n\n` +
      `🔗 View Details: ${window.location.origin}/admin/orders/${orderData.id}`;
    
    const whatsappUrl = `https://wa.me/${import.meta.env.VITE_ADMIN_WHATSAPP}?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  },
  
  sendPaymentNotification: (paymentData) => {
    const message = `💳 *Payment ${paymentData.status}!*\n\n` +
      `📋 Order: ${paymentData.orderNumber}\n` +
      `👤 Customer: ${paymentData.customerName}\n` +
      `💰 Amount: ₹${paymentData.amount}\n` +
      `🆔 Transaction: ${paymentData.transactionId}\n` +
      `⏰ Time: ${new Date().toLocaleString()}\n\n` +
      `${paymentData.status === 'completed' ? '✅ Payment Successful' : '❌ Payment Failed'}`;
    
    const whatsappUrl = `https://wa.me/${import.meta.env.VITE_ADMIN_WHATSAPP}?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  }
};

// Notification service
export const notificationService = {
  // Send email notification to admin
  notifyAdmin: async (type, data) => {
    try {
      const notifications = {
        newOrder: {
          subject: `New Order: ${data.orderNumber}`,
          message: `New order received from ${data.customerName} for ₹${data.total}`
        },
        paymentReceived: {
          subject: `Payment Received: ${data.orderNumber}`,
          message: `Payment of ₹${data.amount} received for order ${data.orderNumber}`
        },
        paymentFailed: {
          subject: `Payment Failed: ${data.orderNumber}`,
          message: `Payment failed for order ${data.orderNumber}. Amount: ₹${data.amount}`
        }
      };

      const notification = notifications[type];
      if (notification) {
        // Send email notification (you can implement this endpoint in backend)
        await api.post('/admin/notify', {
          type,
          subject: notification.subject,
          message: notification.message,
          data
        });
      }
    } catch (error) {
      console.error('Failed to send admin notification:', error);
    }
  },

  // Browser notification
  showBrowserNotification: (title, message, icon = '/favicon.ico') => {
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(title, {
        body: message,
        icon,
        badge: icon
      });
    }
  },

  // Request notification permission
  requestPermission: async () => {
    if ('Notification' in window && Notification.permission === 'default') {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    }
    return Notification.permission === 'granted';
  }
};

// Real-time order tracking
export const orderTracking = {
  // Simulate real-time updates (you can replace with WebSocket)
  subscribeToOrderUpdates: (orderId, callback) => {
    const interval = setInterval(async () => {
      try {
        const order = await ordersAPI.getById(orderId);
        callback(order.data.order);
      } catch (error) {
        console.error('Failed to fetch order updates:', error);
      }
    }, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }
};

export default api;
