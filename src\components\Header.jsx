import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Leaf, ShoppingCart, Menu, X, Search, User, Globe, Bell, MapPin } from 'lucide-react';
import logoImage from '../assets/Untitled design (6).png';

const Header = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [searchOpen, setSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [language, setLanguage] = useState('en');
  const [cartCount, setCartCount] = useState(0);
  const location = useLocation();

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Load cart count from localStorage
  useEffect(() => {
    const updateCartCount = () => {
      const savedCart = localStorage.getItem('vastvik-cart');
      if (savedCart) {
        const cartItems = JSON.parse(savedCart);
        const totalItems = cartItems.reduce((sum, item) => sum + item.quantity, 0);
        setCartCount(totalItems);
      } else {
        setCartCount(0);
      }
    };

    // Initial load
    updateCartCount();

    // Listen for cart updates
    window.addEventListener('cartUpdated', updateCartCount);

    return () => {
      window.removeEventListener('cartUpdated', updateCartCount);
    };
  }, []);

  const toggleLanguage = () => {
    setLanguage(language === 'en' ? 'hi' : 'en');
  };

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // Navigate to products page with search query
      window.location.href = `/products?search=${encodeURIComponent(searchQuery)}`;
    }
  };

  const isHomePage = location.pathname === '/';

  return (
    <header
      className={`fixed w-full z-50 transition-all duration-300 ${
        isScrolled || !isHomePage ? 'bg-white shadow-md py-2' : 'bg-transparent py-4'
      }`}
    >
      <div className="container-custom flex justify-between items-center">
        <Link to="/" className="flex items-center group relative">
          {/* Logo Container with Glow Effect */}
          <div className="relative">
            {/* Background Glow - Responsive */}
            <div className="absolute inset-0 bg-gradient-to-r from-mustard/20 via-yellow-400/30 to-mustard/20
              rounded-full blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500 scale-150
              md:scale-150 sm:scale-125"></div>

            {/* Logo with Multiple Effects - Responsive Sizing */}
            <img
              src={logoImage}
              alt="Vastavik Mustard Oil Logo"
              className="h-10 w-auto object-contain relative z-10 transform
                sm:h-12 md:h-14 lg:h-16
                group-hover:scale-110 group-hover:rotate-2
                transition-all duration-500 ease-out
                filter group-hover:brightness-110 group-hover:contrast-110
                drop-shadow-lg group-hover:drop-shadow-2xl"
            />

            {/* Floating Particles - Responsive */}
            <div className="absolute -top-0.5 -right-0.5 w-1.5 h-1.5 bg-mustard rounded-full
              sm:w-2 sm:h-2 sm:-top-1 sm:-right-1
              animate-pulse opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div className="absolute -bottom-0.5 -left-0.5 w-1 h-1 bg-yellow-400 rounded-full
              sm:w-1.5 sm:h-1.5 sm:-bottom-1 sm:-left-1
              animate-bounce opacity-0 group-hover:opacity-100 transition-opacity duration-500 delay-100"></div>
          </div>

          {/* Enhanced Tagline - Responsive */}
          <div className="ml-2 sm:ml-3 flex flex-col">
            <span className={`text-xs sm:text-sm font-medium tracking-wide ${
              isScrolled || !isHomePage ? 'text-brown/80' : 'text-white/90'
            } group-hover:text-mustard transition-colors duration-300`}>
              {language === 'hi' ? 'गांव से' : 'Village Pure'}
            </span>
            <span className={`text-xs sm:text-xs ${
              isScrolled || !isHomePage ? 'text-brown/60' : 'text-white/70'
            } group-hover:text-mustard/80 transition-colors duration-300 hidden sm:block`}>
              {language === 'hi' ? 'शुद्धता' : 'Authentic'}
            </span>
          </div>
        </Link>

        <nav className="hidden md:block">
          <ul className="flex space-x-8">
            <li>
              <Link
                to="/"
                className={`font-medium hover:text-mustard transition-all duration-300 relative group ${
                  isScrolled || !isHomePage ? 'text-brown' : 'text-white'
                }`}
              >
                {language === 'hi' ? 'घर' : 'Home'}
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-mustard group-hover:w-full transition-all duration-300"></span>
              </Link>
            </li>
            <li>
              <Link
                to="/products"
                className={`font-medium hover:text-mustard transition-all duration-300 relative group ${
                  isScrolled || !isHomePage ? 'text-brown' : 'text-white'
                }`}
              >
                {language === 'hi' ? 'उत्पाद' : 'Products'}
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-mustard group-hover:w-full transition-all duration-300"></span>
              </Link>
            </li>
            <li>
              <Link
                to="/about"
                className={`font-medium hover:text-mustard transition-all duration-300 relative group ${
                  isScrolled || !isHomePage ? 'text-brown' : 'text-white'
                }`}
              >
                {language === 'hi' ? 'हमारे बारे में' : 'About'}
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-mustard group-hover:w-full transition-all duration-300"></span>
              </Link>
            </li>
            <li>
              <Link
                to="/contact"
                className={`font-medium hover:text-mustard transition-all duration-300 relative group ${
                  isScrolled || !isHomePage ? 'text-brown' : 'text-white'
                }`}
              >
                {language === 'hi' ? 'संपर्क' : 'Contact'}
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-mustard group-hover:w-full transition-all duration-300"></span>
              </Link>
            </li>
            {isHomePage && (
              ['Benefits', 'Testimonials', 'Story'].map((item) => (
                <li key={item}>
                  <a
                    href={`#${item.toLowerCase()}`}
                    className={`font-medium hover:text-mustard transition-all duration-300 relative group ${
                      isScrolled ? 'text-brown' : 'text-white'
                    }`}
                  >
                    {language === 'hi' ?
                      (item === 'Benefits' ? 'फायदे' :
                       item === 'Testimonials' ? 'समीक्षा' : 'कहानी') :
                      item}
                    <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-mustard group-hover:w-full transition-all duration-300"></span>
                  </a>
                </li>
              ))
            )}
          </ul>
        </nav>

        <div className="flex items-center space-x-4">
          {/* Search Button with Village Animation */}
          <div className="relative hidden md:block">
            <button
              onClick={() => setSearchOpen(!searchOpen)}
              className={`${isScrolled || !isHomePage ? 'text-brown' : 'text-white'}
                hover:text-mustard transition-all duration-300 transform hover:scale-110`}
            >
              <Search className="h-5 w-5" />
            </button>

            {/* Search Dropdown */}
            {searchOpen && (
              <div className="absolute top-8 right-0 w-80 bg-white rounded-lg shadow-xl border border-mustard/20 p-4 z-50">
                <form onSubmit={handleSearch} className="flex">
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder={language === 'hi' ? 'उत्पाद खोजें...' : 'Search products...'}
                    className="flex-1 px-3 py-2 border border-green/30 rounded-l-md focus:outline-none focus:border-mustard"
                  />
                  <button
                    type="submit"
                    className="px-4 py-2 bg-mustard text-brown rounded-r-md hover:bg-mustard-dark transition-colors"
                  >
                    <Search className="h-4 w-4" />
                  </button>
                </form>
              </div>
            )}
          </div>

          {/* Language Toggle with Village Flag */}
          <button
            onClick={toggleLanguage}
            className={`hidden md:flex items-center space-x-1 ${
              isScrolled || !isHomePage ? 'text-brown' : 'text-white'
            } hover:text-mustard transition-all duration-300 transform hover:scale-110`}
          >
            <Globe className="h-4 w-4" />
            <span className="text-sm font-medium">{language === 'hi' ? 'EN' : 'हि'}</span>
          </button>

          {/* User Account with Village Icon */}
          <Link
            to="/account"
            className={`hidden md:block ${isScrolled || !isHomePage ? 'text-brown' : 'text-white'}
              hover:text-mustard transition-all duration-300 transform hover:scale-110`}
          >
            <User className="h-5 w-5" />
          </Link>

          {/* Enhanced Cart with Village Animation */}
          <Link
            to="/cart"
            className={`${isScrolled || !isHomePage ? 'text-brown' : 'text-white'}
              hover:text-mustard relative group transition-all duration-300 transform hover:scale-110`}
          >
            <ShoppingCart className="h-5 w-5 group-hover:animate-bounce" />
            {cartCount > 0 && (
              <span className="absolute -top-2 -right-2 bg-mustard text-brown text-xs font-bold
                rounded-full h-5 w-5 flex items-center justify-center animate-pulse">
                {cartCount > 99 ? '99+' : cartCount}
              </span>
            )}
          </Link>

          {/* Enhanced Shop Now Button with Village Style */}
          <Link
            to="/products"
            className={`hidden md:block ${
              isScrolled || !isHomePage ? 'bg-green text-white' : 'bg-white text-green'
            } px-4 py-2 rounded-md font-medium shadow-sm hover:shadow-md transition-all duration-300
            transform hover:-translate-y-1 hover:scale-105 border-2 border-transparent hover:border-mustard`}
          >
            {language === 'hi' ? 'खरीदें' : 'Shop Now'}
          </Link>

          <button
            className="md:hidden"
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          >
            {mobileMenuOpen ?
              <X className={`h-6 w-6 ${isScrolled || !isHomePage ? 'text-brown' : 'text-white'}`} /> :
              <Menu className={`h-6 w-6 ${isScrolled || !isHomePage ? 'text-brown' : 'text-white'}`} />
            }
          </button>
        </div>
      </div>

      {mobileMenuOpen && (
        <div className="md:hidden bg-white shadow-lg py-4 animate-fadeIn border-t-2 border-mustard">
          <div className="container-custom">
            {/* Mobile Search */}
            <div className="mb-4 p-3 bg-cream rounded-lg">
              <form onSubmit={handleSearch} className="flex">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder={language === 'hi' ? 'उत्पाद खोजें...' : 'Search products...'}
                  className="flex-1 px-3 py-2 border border-green/30 rounded-l-md focus:outline-none focus:border-mustard"
                />
                <button
                  type="submit"
                  className="px-4 py-2 bg-mustard text-brown rounded-r-md hover:bg-mustard-dark transition-colors"
                >
                  <Search className="h-4 w-4" />
                </button>
              </form>
            </div>

            {/* Mobile Language Toggle */}
            <div className="mb-4 flex justify-center">
              <button
                onClick={toggleLanguage}
                className="flex items-center space-x-2 px-4 py-2 bg-green text-white rounded-md hover:bg-green-dark transition-colors"
              >
                <Globe className="h-4 w-4" />
                <span className="text-sm font-medium">
                  {language === 'hi' ? 'Switch to English' : 'हिंदी में बदलें'}
                </span>
              </button>
            </div>

            <ul className="space-y-4">
              <li>
                <Link
                  to="/"
                  className="block font-medium text-brown hover:text-mustard transition-colors py-2 px-3 rounded hover:bg-cream"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  {language === 'hi' ? 'घर' : 'Home'}
                </Link>
              </li>
              <li>
                <Link
                  to="/products"
                  className="block font-medium text-brown hover:text-mustard transition-colors py-2 px-3 rounded hover:bg-cream"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  {language === 'hi' ? 'उत्पाद' : 'Products'}
                </Link>
              </li>
              <li>
                <Link
                  to="/about"
                  className="block font-medium text-brown hover:text-mustard transition-colors py-2 px-3 rounded hover:bg-cream"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  {language === 'hi' ? 'हमारे बारे में' : 'About'}
                </Link>
              </li>
              <li>
                <Link
                  to="/contact"
                  className="block font-medium text-brown hover:text-mustard transition-colors py-2 px-3 rounded hover:bg-cream"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  {language === 'hi' ? 'संपर्क' : 'Contact'}
                </Link>
              </li>
              <li>
                <Link
                  to="/account"
                  className="block font-medium text-brown hover:text-mustard transition-colors py-2 px-3 rounded hover:bg-cream"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  {language === 'hi' ? 'खाता' : 'Account'}
                </Link>
              </li>
              {isHomePage && (
                ['Benefits', 'Testimonials', 'Story'].map((item) => (
                  <li key={item}>
                    <a
                      href={`#${item.toLowerCase()}`}
                      className="block font-medium text-brown hover:text-mustard transition-colors py-2 px-3 rounded hover:bg-cream"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      {language === 'hi' ?
                        (item === 'Benefits' ? 'फायदे' :
                         item === 'Testimonials' ? 'समीक्षा' : 'कहानी') :
                        item}
                    </a>
                  </li>
                ))
              )}
              <li className="pt-4 border-t border-mustard/20">
                <Link
                  to="/products"
                  className="block font-medium text-white bg-green hover:bg-green-dark transition-colors py-3 px-4 rounded-md text-center"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  {language === 'hi' ? 'अभी खरीदें' : 'Shop Now'}
                </Link>
              </li>
            </ul>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;

