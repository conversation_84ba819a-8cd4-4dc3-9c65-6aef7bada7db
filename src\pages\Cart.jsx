import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Plus, Minus, Trash2, ShoppingBag, ArrowLeft, Heart } from 'lucide-react';

const Cart = () => {
  const [cartItems, setCartItems] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Load cart from localStorage
    const savedCart = localStorage.getItem('vastvik-cart');
    if (savedCart) {
      setCartItems(JSON.parse(savedCart));
    }
    setLoading(false);
  }, []);

  const updateCart = (newCartItems) => {
    setCartItems(newCartItems);
    localStorage.setItem('vastvik-cart', JSON.stringify(newCartItems));
    // Trigger cart count update in header
    window.dispatchEvent(new Event('cartUpdated'));
  };

  const updateQuantity = (id, newQuantity) => {
    if (newQuantity <= 0) {
      removeItem(id);
      return;
    }
    
    const updatedItems = cartItems.map(item =>
      item.id === id ? { ...item, quantity: newQuantity } : item
    );
    updateCart(updatedItems);
  };

  const removeItem = (id) => {
    const updatedItems = cartItems.filter(item => item.id !== id);
    updateCart(updatedItems);
  };

  const getTotalPrice = () => {
    return cartItems.reduce((total, item) => {
      const price = parseInt(item.price.replace('₹', ''));
      return total + (price * item.quantity);
    }, 0);
  };

  const getTotalItems = () => {
    return cartItems.reduce((total, item) => total + item.quantity, 0);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-cream flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green mx-auto mb-4"></div>
          <p className="text-brown">Loading your cart...</p>
        </div>
      </div>
    );
  }

  if (cartItems.length === 0) {
    return (
      <div className="min-h-screen bg-cream pt-24">
        <div className="container-custom">
          <div className="text-center py-16">
            <ShoppingBag className="h-24 w-24 text-brown/30 mx-auto mb-6" />
            <h1 className="text-3xl font-bold text-brown mb-4">Your Cart is Empty</h1>
            <p className="text-brown/70 mb-8 max-w-md mx-auto">
              Looks like you haven't added any items to your cart yet. 
              Explore our pure, village-made mustard oil collection.
            </p>
            <Link 
              to="/products"
              className="btn-primary inline-flex items-center space-x-2"
            >
              <ArrowLeft className="h-5 w-5" />
              <span>Continue Shopping</span>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-cream pt-24">
      <div className="container-custom">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-brown mb-2">Shopping Cart</h1>
            <p className="text-brown/70">
              {getTotalItems()} {getTotalItems() === 1 ? 'item' : 'items'} in your cart
            </p>
          </div>
          <Link 
            to="/products"
            className="text-green hover:text-green-dark transition-colors flex items-center space-x-2"
          >
            <ArrowLeft className="h-5 w-5" />
            <span>Continue Shopping</span>
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2">
            <div className="space-y-4">
              {cartItems.map((item) => (
                <div key={item.id} className="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow">
                  <div className="flex items-center space-x-4">
                    <img 
                      src={item.image} 
                      alt={item.name}
                      className="w-20 h-20 object-cover rounded-lg"
                    />
                    
                    <div className="flex-1">
                      <h3 className="font-semibold text-brown mb-1">{item.name}</h3>
                      <p className="text-green font-medium">{item.size}</p>
                      <p className="text-2xl font-bold text-brown">{item.price}</p>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <button
                        onClick={() => updateQuantity(item.id, item.quantity - 1)}
                        className="w-8 h-8 rounded-full bg-cream hover:bg-mustard/20 flex items-center justify-center transition-colors"
                      >
                        <Minus className="h-4 w-4 text-brown" />
                      </button>
                      
                      <span className="w-12 text-center font-semibold text-brown">
                        {item.quantity}
                      </span>
                      
                      <button
                        onClick={() => updateQuantity(item.id, item.quantity + 1)}
                        className="w-8 h-8 rounded-full bg-cream hover:bg-mustard/20 flex items-center justify-center transition-colors"
                      >
                        <Plus className="h-4 w-4 text-brown" />
                      </button>
                    </div>
                    
                    <div className="flex flex-col space-y-2">
                      <button
                        onClick={() => removeItem(item.id)}
                        className="text-red-500 hover:text-red-700 transition-colors"
                      >
                        <Trash2 className="h-5 w-5" />
                      </button>
                      <button className="text-brown hover:text-mustard transition-colors">
                        <Heart className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg p-6 shadow-md sticky top-24">
              <h2 className="text-xl font-bold text-brown mb-6">Order Summary</h2>
              
              <div className="space-y-4 mb-6">
                <div className="flex justify-between">
                  <span className="text-brown/70">Subtotal</span>
                  <span className="font-semibold text-brown">₹{getTotalPrice()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-brown/70">Shipping</span>
                  <span className="font-semibold text-green">Free</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-brown/70">Tax</span>
                  <span className="font-semibold text-brown">₹0</span>
                </div>
                <div className="border-t border-brown/20 pt-4">
                  <div className="flex justify-between">
                    <span className="text-lg font-bold text-brown">Total</span>
                    <span className="text-lg font-bold text-brown">₹{getTotalPrice()}</span>
                  </div>
                </div>
              </div>
              
              <Link 
                to="/checkout"
                className="w-full btn-primary text-center block mb-4"
              >
                Proceed to Checkout
              </Link>
              
              <div className="text-center text-sm text-brown/70">
                <p className="mb-2">🚚 Free delivery on all orders</p>
                <p className="mb-2">🔒 Secure payment</p>
                <p>📞 24/7 customer support</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Cart;
