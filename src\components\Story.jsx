import React, { useState } from 'react';
import { X, Play } from 'lucide-react';

const Story = () => {
  const [showModal, setShowModal] = useState(false);

  return (
    <section id="story" className="section-padding bg-cream">
      <div className="container-custom">
        <h2 className="section-title">Our Village Story</h2>
        <p className="section-subtitle">
          Discover the journey behind every bottle of Vastvik Mustard Oil and the village 
          traditions we honor.
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
          <div>
            <h3 className="text-2xl font-semibold mb-4 text-brown">A Heritage of Purity</h3>
            <p className="mb-4">
              For generations, our family has been crafting pure mustard oil in the heart of rural 
              India. We use the same traditional wooden kolhu (oil press) that our ancestors used,
              ensuring the authentic taste and benefits are preserved.
            </p>
            <p className="mb-6">
              Our mustard seeds are grown without pesticides in the fertile soils of North India, 
              then cold-pressed to extract oil that retains all its natural goodness.
            </p>
            
            <button 
              onClick={() => setShowModal(true)}
              className="flex items-center space-x-2 px-4 py-2 bg-green text-white rounded-md hover:bg-green-dark transition-colors"
            >
              <Play className="h-5 w-5" />
              <span>Watch Our Story</span>
            </button>
          </div>
          
          <div className="relative">
            <div className="aspect-w-16 aspect-h-9 relative overflow-hidden rounded-lg shadow-xl">
              <img 
                src="https://images.pexels.com/photos/2387319/pexels-photo-2387319.jpeg" 
                alt="Traditional oil press" 
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 flex items-center justify-center">
                <button 
                  onClick={() => setShowModal(true)}
                  className="w-16 h-16 bg-mustard rounded-full flex items-center justify-center shadow-lg hover:bg-mustard-dark transition-colors"
                >
                  <Play className="h-8 w-8 text-white" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Video Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
          <div className="relative w-full max-w-4xl">
            <button 
              onClick={() => setShowModal(false)}
              className="absolute -top-12 right-0 text-white hover:text-mustard"
            >
              <X className="h-8 w-8" />
            </button>
            
            <div className="aspect-w-16 aspect-h-9 bg-black rounded-lg overflow-hidden">
              <iframe
                width="100%"
                height="100%"
                src="https://www.youtube.com/embed/dQw4w9WgXcQ?autoplay=1"
                title="Vastvik Mustard Oil Story"
                frameBorder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
              ></iframe>
            </div>
          </div>
        </div>
      )}
    </section>
  );
};

export default Story;