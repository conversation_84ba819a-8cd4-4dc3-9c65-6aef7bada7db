import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { adminAPI, ordersAPI, notificationService, whatsappAPI } from '../services/api';

const AdminContext = createContext();

// Initial state
const initialState = {
  dashboard: {
    overview: {},
    recentOrders: [],
    charts: {},
    topProducts: [],
    lowStockProducts: []
  },
  orders: [],
  users: [],
  analytics: {},
  notifications: [],
  loading: false,
  error: null
};

// Action types
const ADMIN_ACTIONS = {
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
  SET_DASHBOARD: 'SET_DASHBOARD',
  SET_ORDERS: 'SET_ORDERS',
  SET_USERS: 'SET_USERS',
  SET_ANALYTICS: 'SET_ANALYTICS',
  ADD_NOTIFICATION: 'ADD_NOTIFICATION',
  REMOVE_NOTIFICATION: 'REMOVE_NOTIFICATION',
  UPDATE_ORDER_STATUS: 'UPDATE_ORDER_STATUS',
  ADD_NEW_ORDER: 'ADD_NEW_ORDER'
};

// Reducer
const adminReducer = (state, action) => {
  switch (action.type) {
    case ADMIN_ACTIONS.SET_LOADING:
      return { ...state, loading: action.payload };
    
    case ADMIN_ACTIONS.SET_ERROR:
      return { ...state, error: action.payload, loading: false };
    
    case ADMIN_ACTIONS.SET_DASHBOARD:
      return { ...state, dashboard: action.payload, loading: false };
    
    case ADMIN_ACTIONS.SET_ORDERS:
      return { ...state, orders: action.payload, loading: false };
    
    case ADMIN_ACTIONS.SET_USERS:
      return { ...state, users: action.payload, loading: false };
    
    case ADMIN_ACTIONS.SET_ANALYTICS:
      return { ...state, analytics: action.payload, loading: false };
    
    case ADMIN_ACTIONS.ADD_NOTIFICATION:
      return {
        ...state,
        notifications: [action.payload, ...state.notifications.slice(0, 9)] // Keep last 10
      };
    
    case ADMIN_ACTIONS.REMOVE_NOTIFICATION:
      return {
        ...state,
        notifications: state.notifications.filter(n => n.id !== action.payload)
      };
    
    case ADMIN_ACTIONS.UPDATE_ORDER_STATUS:
      return {
        ...state,
        orders: state.orders.map(order =>
          order._id === action.payload.orderId
            ? { ...order, status: action.payload.status }
            : order
        ),
        dashboard: {
          ...state.dashboard,
          recentOrders: state.dashboard.recentOrders.map(order =>
            order._id === action.payload.orderId
              ? { ...order, status: action.payload.status }
              : order
          )
        }
      };
    
    case ADMIN_ACTIONS.ADD_NEW_ORDER:
      return {
        ...state,
        orders: [action.payload, ...state.orders],
        dashboard: {
          ...state.dashboard,
          recentOrders: [action.payload, ...state.dashboard.recentOrders.slice(0, 9)]
        }
      };
    
    default:
      return state;
  }
};

// Admin Provider
export const AdminProvider = ({ children }) => {
  const [state, dispatch] = useReducer(adminReducer, initialState);

  // Load dashboard data
  const loadDashboard = async (period = '30') => {
    try {
      dispatch({ type: ADMIN_ACTIONS.SET_LOADING, payload: true });
      const response = await adminAPI.getDashboard(period);
      dispatch({ type: ADMIN_ACTIONS.SET_DASHBOARD, payload: response.data });
    } catch (error) {
      dispatch({ type: ADMIN_ACTIONS.SET_ERROR, payload: error.message });
    }
  };

  // Load orders
  const loadOrders = async (params = {}) => {
    try {
      dispatch({ type: ADMIN_ACTIONS.SET_LOADING, payload: true });
      const response = await adminAPI.getOrders(params);
      dispatch({ type: ADMIN_ACTIONS.SET_ORDERS, payload: response.data.orders });
    } catch (error) {
      dispatch({ type: ADMIN_ACTIONS.SET_ERROR, payload: error.message });
    }
  };

  // Load users
  const loadUsers = async (params = {}) => {
    try {
      dispatch({ type: ADMIN_ACTIONS.SET_LOADING, payload: true });
      const response = await adminAPI.getUsers(params);
      dispatch({ type: ADMIN_ACTIONS.SET_USERS, payload: response.data.users });
    } catch (error) {
      dispatch({ type: ADMIN_ACTIONS.SET_ERROR, payload: error.message });
    }
  };

  // Load analytics
  const loadAnalytics = async (period = '30') => {
    try {
      dispatch({ type: ADMIN_ACTIONS.SET_LOADING, payload: true });
      const response = await adminAPI.getAnalytics(period);
      dispatch({ type: ADMIN_ACTIONS.SET_ANALYTICS, payload: response.data });
    } catch (error) {
      dispatch({ type: ADMIN_ACTIONS.SET_ERROR, payload: error.message });
    }
  };

  // Update order status
  const updateOrderStatus = async (orderId, status, message) => {
    try {
      await ordersAPI.updateStatus(orderId, status, message);
      dispatch({
        type: ADMIN_ACTIONS.UPDATE_ORDER_STATUS,
        payload: { orderId, status }
      });

      // Add notification
      addNotification({
        type: 'success',
        title: 'Order Updated',
        message: `Order status updated to ${status}`,
        timestamp: new Date()
      });

      return true;
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'Update Failed',
        message: error.message,
        timestamp: new Date()
      });
      return false;
    }
  };

  // Add notification
  const addNotification = (notification) => {
    const notificationWithId = {
      ...notification,
      id: Date.now() + Math.random()
    };
    
    dispatch({
      type: ADMIN_ACTIONS.ADD_NOTIFICATION,
      payload: notificationWithId
    });

    // Auto remove after 5 seconds
    setTimeout(() => {
      removeNotification(notificationWithId.id);
    }, 5000);

    // Show browser notification
    notificationService.showBrowserNotification(
      notification.title,
      notification.message
    );
  };

  // Remove notification
  const removeNotification = (id) => {
    dispatch({
      type: ADMIN_ACTIONS.REMOVE_NOTIFICATION,
      payload: id
    });
  };

  // Handle new order (called when order is created)
  const handleNewOrder = async (orderData) => {
    // Add to state
    dispatch({
      type: ADMIN_ACTIONS.ADD_NEW_ORDER,
      payload: orderData
    });

    // Add notification
    addNotification({
      type: 'info',
      title: 'New Order Received!',
      message: `Order ${orderData.orderNumber} from ${orderData.user?.name}`,
      timestamp: new Date()
    });

    // Send WhatsApp notification
    whatsappAPI.sendOrderNotification({
      id: orderData._id,
      orderNumber: orderData.orderNumber,
      customerName: orderData.user?.name || orderData.shippingAddress?.name,
      phone: orderData.shippingAddress?.phone,
      total: orderData.pricing?.total,
      paymentMethod: orderData.payment?.method,
      address: `${orderData.shippingAddress?.street}, ${orderData.shippingAddress?.city}`
    });

    // Send email notification
    await notificationService.notifyAdmin('newOrder', {
      orderNumber: orderData.orderNumber,
      customerName: orderData.user?.name || orderData.shippingAddress?.name,
      total: orderData.pricing?.total
    });
  };

  // Handle payment update
  const handlePaymentUpdate = async (paymentData) => {
    // Add notification
    addNotification({
      type: paymentData.status === 'completed' ? 'success' : 'error',
      title: `Payment ${paymentData.status}`,
      message: `Payment for order ${paymentData.orderNumber}`,
      timestamp: new Date()
    });

    // Send WhatsApp notification
    whatsappAPI.sendPaymentNotification(paymentData);

    // Send email notification
    await notificationService.notifyAdmin(
      paymentData.status === 'completed' ? 'paymentReceived' : 'paymentFailed',
      paymentData
    );
  };

  // Real-time order monitoring
  useEffect(() => {
    const interval = setInterval(async () => {
      try {
        // Check for new orders every 30 seconds
        const response = await adminAPI.getOrders({ 
          page: 1, 
          limit: 5, 
          sortBy: 'newest' 
        });
        
        const latestOrders = response.data.orders;
        const currentOrderIds = state.orders.map(o => o._id);
        
        // Check for new orders
        latestOrders.forEach(order => {
          if (!currentOrderIds.includes(order._id)) {
            handleNewOrder(order);
          }
        });
      } catch (error) {
        console.error('Failed to check for new orders:', error);
      }
    }, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, [state.orders]);

  // Request notification permission on mount
  useEffect(() => {
    notificationService.requestPermission();
  }, []);

  const value = {
    ...state,
    loadDashboard,
    loadOrders,
    loadUsers,
    loadAnalytics,
    updateOrderStatus,
    addNotification,
    removeNotification,
    handleNewOrder,
    handlePaymentUpdate
  };

  return (
    <AdminContext.Provider value={value}>
      {children}
    </AdminContext.Provider>
  );
};

// Custom hook
export const useAdmin = () => {
  const context = useContext(AdminContext);
  if (!context) {
    throw new Error('useAdmin must be used within AdminProvider');
  }
  return context;
};

export default AdminContext;
