import React, { useState, useEffect } from 'react';
import { Link, useSearchParams } from 'react-router-dom';
import { ShoppingCart, Plus, Filter, Search, Heart, Star } from 'lucide-react';
import { products } from '../data/products';

const Products = () => {
  const [searchParams] = useSearchParams();
  const [filteredProducts, setFilteredProducts] = useState(products);
  const [searchQuery, setSearchQuery] = useState(searchParams.get('search') || '');
  const [sortBy, setSortBy] = useState('name');
  const [wishlist, setWishlist] = useState([]);
  const [addedToCart, setAddedToCart] = useState({});

  useEffect(() => {
    // Load wishlist from localStorage
    const savedWishlist = localStorage.getItem('vastvik-wishlist');
    if (savedWishlist) {
      setWishlist(JSON.parse(savedWishlist));
    }
  }, []);

  useEffect(() => {
    // Filter and sort products
    let filtered = products.filter(product =>
      product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      product.size.toLowerCase().includes(searchQuery.toLowerCase())
    );

    // Sort products
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'price-low':
          return parseInt(a.price.replace('₹', '')) - parseInt(b.price.replace('₹', ''));
        case 'price-high':
          return parseInt(b.price.replace('₹', '')) - parseInt(a.price.replace('₹', ''));
        case 'name':
        default:
          return a.name.localeCompare(b.name);
      }
    });

    setFilteredProducts(filtered);
  }, [searchQuery, sortBy]);

  const addToCart = (product) => {
    const savedCart = localStorage.getItem('vastvik-cart');
    let cartItems = savedCart ? JSON.parse(savedCart) : [];

    const existingItem = cartItems.find(item => item.id === product.id);

    if (existingItem) {
      existingItem.quantity += 1;
    } else {
      cartItems.push({ ...product, quantity: 1 });
    }

    localStorage.setItem('vastvik-cart', JSON.stringify(cartItems));

    // Show feedback
    setAddedToCart({ ...addedToCart, [product.id]: true });
    setTimeout(() => {
      setAddedToCart({ ...addedToCart, [product.id]: false });
    }, 2000);

    // Trigger cart update event
    window.dispatchEvent(new Event('cartUpdated'));
  };

  const toggleWishlist = (product) => {
    let newWishlist;
    if (wishlist.find(item => item.id === product.id)) {
      newWishlist = wishlist.filter(item => item.id !== product.id);
    } else {
      newWishlist = [...wishlist, product];
    }

    setWishlist(newWishlist);
    localStorage.setItem('vastvik-wishlist', JSON.stringify(newWishlist));
  };

  const createWhatsAppLink = (product) => {
    const message = `Hi, I'm interested in buying ${product.name} (${product.size}) priced at ${product.price}. Please provide details for ordering.`;
    const encodedMessage = encodeURIComponent(message);
    return `https://wa.me/918979799769?text=${encodedMessage}`;
  };

  return (
    <section className="section-padding bg-cream pt-24">
      <div className="container-custom">
        <h1 className="section-title">Our Village Products</h1>
        <p className="section-subtitle">
          Discover our range of pure, cold-pressed mustard oil products crafted with traditional village methods.
        </p>

        {/* Search and Filter Bar */}
        <div className="mb-12 bg-white p-6 rounded-lg shadow-md">
          <div className="flex flex-col md:flex-row gap-4 items-center">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-brown/50 h-5 w-5" />
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search products..."
                className="w-full pl-10 pr-4 py-3 border border-brown/20 rounded-md focus:outline-none focus:border-mustard"
              />
            </div>

            <div className="flex items-center space-x-4">
              <Filter className="h-5 w-5 text-brown" />
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-4 py-3 border border-brown/20 rounded-md focus:outline-none focus:border-mustard"
              >
                <option value="name">Sort by Name</option>
                <option value="price-low">Price: Low to High</option>
                <option value="price-high">Price: High to Low</option>
              </select>
            </div>
          </div>

          {searchQuery && (
            <div className="mt-4 text-brown/70">
              Showing {filteredProducts.length} result(s) for "{searchQuery}"
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredProducts.map((product) => (
            <div key={product.id} className="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 village-shimmer">
              <div className="relative h-64 overflow-hidden">
                <img
                  src={product.image}
                  alt={product.name}
                  className="w-full h-full object-cover hover:scale-105 transition-transform duration-500"
                />

                {/* Wishlist Button */}
                <button
                  onClick={() => toggleWishlist(product)}
                  className="absolute top-4 left-4 p-2 bg-white/80 rounded-full hover:bg-white transition-colors"
                >
                  <Heart
                    className={`h-5 w-5 ${
                      wishlist.find(item => item.id === product.id)
                        ? 'text-red-500 fill-red-500'
                        : 'text-brown'
                    }`}
                  />
                </button>

                {product.badge && (
                  <span className="absolute top-4 right-4 bg-mustard text-brown px-3 py-1 rounded-full text-sm font-medium village-pulse">
                    {product.badge}
                  </span>
                )}

                {/* Rating Stars */}
                <div className="absolute bottom-4 left-4 flex space-x-1">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 fill-mustard text-mustard" />
                  ))}
                </div>
              </div>

              <div className="p-6">
                <h3 className="text-xl font-semibold text-brown mb-2">{product.name}</h3>
                <p className="text-lg text-green mb-1">{product.size}</p>
                <p className="text-2xl font-bold text-brown mb-4">{product.price}</p>
                <p className="text-brown/70 mb-6 text-sm">{product.shortDescription}</p>

                <div className="space-y-3">
                  <div className="flex space-x-2">
                    <Link
                      to={`/products/${product.id}`}
                      className="flex-1 text-center px-4 py-3 bg-brown text-white font-semibold rounded-md hover:bg-brown-dark transition-all duration-300 transform hover:-translate-y-1"
                    >
                      View Details
                    </Link>

                    <button
                      onClick={() => addToCart(product)}
                      disabled={addedToCart[product.id]}
                      className={`px-4 py-3 rounded-md font-semibold transition-all duration-300 transform hover:-translate-y-1 ${
                        addedToCart[product.id]
                          ? 'bg-green text-white'
                          : 'bg-mustard text-brown hover:bg-mustard-dark'
                      }`}
                    >
                      {addedToCart[product.id] ? (
                        <span>✓</span>
                      ) : (
                        <Plus className="h-5 w-5" />
                      )}
                    </button>
                  </div>

                  <button
                    onClick={() => addToCart(product)}
                    className="flex items-center justify-center w-full px-6 py-3 bg-green text-white font-semibold rounded-md hover:bg-green-dark transition-all duration-300 transform hover:-translate-y-1"
                  >
                    <ShoppingCart className="h-5 w-5 mr-2" />
                    Add to Cart
                  </button>

                  <a
                    href={createWhatsAppLink(product)}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center justify-center w-full px-6 py-3 bg-[#25D366] text-white font-semibold rounded-md hover:bg-[#25D366]/90 transition-all duration-300 transform hover:-translate-y-1"
                  >
                    <svg className="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                    </svg>
                    WhatsApp Order
                  </a>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Products;




