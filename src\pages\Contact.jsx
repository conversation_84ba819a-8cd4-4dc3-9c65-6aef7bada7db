import React, { useState } from 'react';
import { MapPin, Phone, Mail, Clock, Send, MessageCircle } from 'lucide-react';

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState(null);

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate form submission
    setTimeout(() => {
      setSubmitStatus('success');
      setIsSubmitting(false);
      setFormData({
        name: '',
        email: '',
        phone: '',
        subject: '',
        message: ''
      });
    }, 2000);
  };

  const contactInfo = [
    {
      icon: <MapPin className="h-6 w-6" />,
      title: 'Visit Our Village',
      details: [
        'Vastvik Oil Mills',
        'Village Khanna, Tehsil Samrala',
        'District Ludhiana, Punjab 141114'
      ]
    },
    {
      icon: <Phone className="h-6 w-6" />,
      title: 'Call Us',
      details: [
        '+91 98765 43210',
        '+91 98765 43211',
        'Mon-Sat: 9:00 AM - 6:00 PM'
      ]
    },
    {
      icon: <Mail className="h-6 w-6" />,
      title: 'Email Us',
      details: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ]
    },
    {
      icon: <Clock className="h-6 w-6" />,
      title: 'Business Hours',
      details: [
        'Monday - Saturday: 9:00 AM - 6:00 PM',
        'Sunday: 10:00 AM - 4:00 PM',
        'Closed on major festivals'
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-cream pt-24">
      {/* Hero Section */}
      <section className="section-padding bg-village-scene bg-cover bg-center relative">
        <div className="absolute inset-0 bg-brown/70"></div>
        <div className="container-custom relative z-10 text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Get in Touch
          </h1>
          <p className="text-xl text-white/90 max-w-3xl mx-auto">
            Have questions about our products? Want to visit our village? 
            We'd love to hear from you and share our story.
          </p>
        </div>
      </section>

      {/* Contact Information */}
      <section className="section-padding">
        <div className="container-custom">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            {contactInfo.map((info, index) => (
              <div key={index} className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow text-center">
                <div className="flex justify-center mb-4">
                  <div className="p-3 bg-mustard/20 rounded-full text-green">
                    {info.icon}
                  </div>
                </div>
                <h3 className="text-lg font-semibold text-brown mb-3">{info.title}</h3>
                <div className="space-y-1">
                  {info.details.map((detail, idx) => (
                    <p key={idx} className="text-brown/70 text-sm">{detail}</p>
                  ))}
                </div>
              </div>
            ))}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div className="bg-white p-8 rounded-lg shadow-md">
              <h2 className="text-2xl font-bold text-brown mb-6">Send us a Message</h2>
              
              {submitStatus === 'success' && (
                <div className="mb-6 p-4 bg-green/10 border border-green/20 rounded-lg">
                  <p className="text-green font-medium">
                    Thank you for your message! We'll get back to you within 24 hours.
                  </p>
                </div>
              )}

              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="name" className="block text-brown font-medium mb-2">
                      Full Name *
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-brown/20 rounded-md focus:outline-none focus:border-mustard transition-colors"
                      placeholder="Your full name"
                    />
                  </div>
                  <div>
                    <label htmlFor="email" className="block text-brown font-medium mb-2">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-brown/20 rounded-md focus:outline-none focus:border-mustard transition-colors"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="phone" className="block text-brown font-medium mb-2">
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-brown/20 rounded-md focus:outline-none focus:border-mustard transition-colors"
                      placeholder="+91 98765 43210"
                    />
                  </div>
                  <div>
                    <label htmlFor="subject" className="block text-brown font-medium mb-2">
                      Subject *
                    </label>
                    <select
                      id="subject"
                      name="subject"
                      value={formData.subject}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-brown/20 rounded-md focus:outline-none focus:border-mustard transition-colors"
                    >
                      <option value="">Select a subject</option>
                      <option value="product-inquiry">Product Inquiry</option>
                      <option value="bulk-order">Bulk Order</option>
                      <option value="quality-concern">Quality Concern</option>
                      <option value="partnership">Partnership</option>
                      <option value="visit-request">Village Visit Request</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label htmlFor="message" className="block text-brown font-medium mb-2">
                    Message *
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    required
                    rows={6}
                    className="w-full px-4 py-3 border border-brown/20 rounded-md focus:outline-none focus:border-mustard transition-colors resize-vertical"
                    placeholder="Tell us how we can help you..."
                  ></textarea>
                </div>

                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full btn-primary flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-brown"></div>
                      <span>Sending...</span>
                    </>
                  ) : (
                    <>
                      <Send className="h-5 w-5" />
                      <span>Send Message</span>
                    </>
                  )}
                </button>
              </form>
            </div>

            {/* Map and Additional Info */}
            <div className="space-y-8">
              {/* WhatsApp Contact */}
              <div className="bg-[#25D366]/10 p-6 rounded-lg border border-[#25D366]/20">
                <div className="flex items-center space-x-3 mb-4">
                  <MessageCircle className="h-6 w-6 text-[#25D366]" />
                  <h3 className="text-lg font-semibold text-brown">Quick WhatsApp Support</h3>
                </div>
                <p className="text-brown/70 mb-4">
                  Get instant answers to your questions. Our team is available on WhatsApp 
                  for immediate assistance with orders, product information, and more.
                </p>
                <a 
                  href="https://wa.me/919876543210" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="inline-flex items-center space-x-2 px-6 py-3 bg-[#25D366] text-white font-semibold rounded-md hover:bg-[#25D366]/90 transition-colors"
                >
                  <MessageCircle className="h-5 w-5" />
                  <span>Chat with Us</span>
                </a>
              </div>

              {/* Village Visit */}
              <div className="bg-mustard/10 p-6 rounded-lg border border-mustard/20">
                <h3 className="text-lg font-semibold text-brown mb-4">Visit Our Village</h3>
                <p className="text-brown/70 mb-4">
                  Experience the traditional oil extraction process firsthand! We welcome 
                  visitors to our village facility. See how we maintain centuries-old 
                  traditions while ensuring modern quality standards.
                </p>
                <div className="space-y-2 text-sm text-brown/70">
                  <p><strong>Best time to visit:</strong> October to March</p>
                  <p><strong>Duration:</strong> 2-3 hours guided tour</p>
                  <p><strong>Includes:</strong> Oil extraction demo, tasting session, village lunch</p>
                </div>
              </div>

              {/* Map Placeholder */}
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h3 className="text-lg font-semibold text-brown mb-4">Find Us</h3>
                <div className="bg-cream h-64 rounded-lg flex items-center justify-center">
                  <div className="text-center text-brown/70">
                    <MapPin className="h-12 w-12 mx-auto mb-2" />
                    <p>Interactive Map</p>
                    <p className="text-sm">Sasni, Utter Pradesh</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="section-padding bg-white">
        <div className="container-custom">
          <h2 className="section-title">Frequently Asked Questions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="space-y-6">
              <div>
                <h3 className="font-semibold text-brown mb-2">How is your oil different from commercial brands?</h3>
                <p className="text-brown/70 text-sm">
                  Our oil is cold-pressed using traditional wooden kolhu methods, preserving all natural nutrients and authentic taste.
                </p>
              </div>
              <div>
                <h3 className="font-semibold text-brown mb-2">Do you offer bulk orders for restaurants?</h3>
                <p className="text-brown/70 text-sm">
                  Yes, we provide special pricing for bulk orders. Contact us for customized packages for restaurants and hotels.
                </p>
              </div>
              <div>
                <h3 className="font-semibold text-brown mb-2">What is your return policy?</h3>
                <p className="text-brown/70 text-sm">
                  We offer 100% satisfaction guarantee. If you're not happy with the quality, we'll provide a full refund within 30 days.
                </p>
              </div>
            </div>
            <div className="space-y-6">
              <div>
                <h3 className="font-semibold text-brown mb-2">How long does shipping take?</h3>
                <p className="text-brown/70 text-sm">
                  We ship within 24 hours. Delivery typically takes 2-5 business days depending on your location.
                </p>
              </div>
              <div>
                <h3 className="font-semibold text-brown mb-2">Can I visit your production facility?</h3>
                <p className="text-brown/70 text-sm">
                  Absolutely! We encourage visits to see our traditional methods. Please contact us to schedule a tour.
                </p>
              </div>
              <div>
                <h3 className="font-semibold text-brown mb-2">Do you have organic certification?</h3>
                <p className="text-brown/70 text-sm">
                  Yes, our products are certified organic and we follow strict quality control measures throughout the process.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Contact;
