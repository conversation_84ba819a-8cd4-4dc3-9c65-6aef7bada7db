const express = require('express');
const { body, validationResult } = require('express-validator');
const { protect, adminOrModerator } = require('../middleware/auth');
const { sendEmail } = require('../utils/email');
const router = express.Router();

// Simple in-memory storage for contact messages (you can replace with MongoDB model)
const contactMessages = [];

// @desc    Send contact message
// @route   POST /api/v1/contact/message
// @access  Public
router.post('/message', [
  body('name')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Name must be between 2 and 50 characters'),
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('phone')
    .optional()
    .matches(/^[6-9]\d{9}$/)
    .withMessage('Please provide a valid Indian phone number'),
  body('subject')
    .trim()
    .isLength({ min: 5, max: 100 })
    .withMessage('Subject must be between 5 and 100 characters'),
  body('message')
    .trim()
    .isLength({ min: 10, max: 1000 })
    .withMessage('Message must be between 10 and 1000 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { name, email, phone, subject, message } = req.body;

    // Create contact message object
    const contactMessage = {
      id: Date.now().toString(),
      name,
      email,
      phone,
      subject,
      message,
      createdAt: new Date(),
      status: 'new',
      replied: false
    };

    // Store message (in production, save to database)
    contactMessages.push(contactMessage);

    // Send email notification to admin
    try {
      await sendEmail({
        to: process.env.ADMIN_EMAIL || '<EMAIL>',
        subject: `New Contact Message: ${subject}`,
        template: 'contactNotification',
        data: {
          name,
          email,
          phone,
          subject,
          message,
          timestamp: new Date().toLocaleString()
        }
      });
    } catch (emailError) {
      console.error('Failed to send admin notification:', emailError);
    }

    // Send auto-reply to user
    try {
      await sendEmail({
        to: email,
        subject: 'Thank you for contacting Vastavik Mustard Oil',
        template: 'contactAutoReply',
        data: {
          name,
          subject,
          message
        }
      });
    } catch (emailError) {
      console.error('Failed to send auto-reply:', emailError);
    }

    res.status(200).json({
      success: true,
      message: 'Your message has been sent successfully. We will get back to you soon!',
      data: {
        messageId: contactMessage.id,
        estimatedResponseTime: '24-48 hours'
      }
    });

  } catch (error) {
    console.error('Contact message error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while sending message'
    });
  }
});

// @desc    Get all contact messages
// @route   GET /api/v1/contact/messages
// @access  Private/Admin
router.get('/messages', protect, adminOrModerator, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      status = 'all',
      sortBy = 'newest'
    } = req.query;

    let filteredMessages = [...contactMessages];

    // Filter by status
    if (status !== 'all') {
      filteredMessages = filteredMessages.filter(msg => msg.status === status);
    }

    // Sort messages
    filteredMessages.sort((a, b) => {
      if (sortBy === 'oldest') {
        return new Date(a.createdAt) - new Date(b.createdAt);
      }
      return new Date(b.createdAt) - new Date(a.createdAt);
    });

    // Pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedMessages = filteredMessages.slice(startIndex, endIndex);

    // Statistics
    const stats = {
      total: contactMessages.length,
      new: contactMessages.filter(msg => msg.status === 'new').length,
      inProgress: contactMessages.filter(msg => msg.status === 'in-progress').length,
      resolved: contactMessages.filter(msg => msg.status === 'resolved').length,
      replied: contactMessages.filter(msg => msg.replied).length
    };

    res.status(200).json({
      success: true,
      data: {
        messages: paginatedMessages,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(filteredMessages.length / limit),
          totalMessages: filteredMessages.length,
          hasNextPage: endIndex < filteredMessages.length,
          hasPrevPage: page > 1
        },
        stats
      }
    });

  } catch (error) {
    console.error('Get contact messages error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching messages'
    });
  }
});

// @desc    Get single contact message
// @route   GET /api/v1/contact/messages/:id
// @access  Private/Admin
router.get('/messages/:id', protect, adminOrModerator, async (req, res) => {
  try {
    const message = contactMessages.find(msg => msg.id === req.params.id);

    if (!message) {
      return res.status(404).json({
        success: false,
        message: 'Contact message not found'
      });
    }

    res.status(200).json({
      success: true,
      data: { message }
    });

  } catch (error) {
    console.error('Get contact message error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching message'
    });
  }
});

// @desc    Update contact message status
// @route   PUT /api/v1/contact/messages/:id/status
// @access  Private/Admin
router.put('/messages/:id/status', protect, adminOrModerator, [
  body('status')
    .isIn(['new', 'in-progress', 'resolved'])
    .withMessage('Status must be one of: new, in-progress, resolved'),
  body('notes')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Notes cannot exceed 500 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { status, notes } = req.body;
    const messageIndex = contactMessages.findIndex(msg => msg.id === req.params.id);

    if (messageIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Contact message not found'
      });
    }

    // Update message
    contactMessages[messageIndex].status = status;
    contactMessages[messageIndex].updatedAt = new Date();
    contactMessages[messageIndex].updatedBy = req.user.id;

    if (notes) {
      contactMessages[messageIndex].adminNotes = notes;
    }

    res.status(200).json({
      success: true,
      message: 'Message status updated successfully',
      data: { message: contactMessages[messageIndex] }
    });

  } catch (error) {
    console.error('Update message status error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating message status'
    });
  }
});

// @desc    Reply to contact message
// @route   POST /api/v1/contact/messages/:id/reply
// @access  Private/Admin
router.post('/messages/:id/reply', protect, adminOrModerator, [
  body('replyMessage')
    .trim()
    .isLength({ min: 10, max: 2000 })
    .withMessage('Reply message must be between 10 and 2000 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { replyMessage } = req.body;
    const messageIndex = contactMessages.findIndex(msg => msg.id === req.params.id);

    if (messageIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Contact message not found'
      });
    }

    const originalMessage = contactMessages[messageIndex];

    // Send reply email
    try {
      await sendEmail({
        to: originalMessage.email,
        subject: `Re: ${originalMessage.subject}`,
        template: 'contactReply',
        data: {
          name: originalMessage.name,
          originalSubject: originalMessage.subject,
          originalMessage: originalMessage.message,
          replyMessage,
          adminName: req.user.name
        }
      });

      // Update message
      contactMessages[messageIndex].replied = true;
      contactMessages[messageIndex].replyMessage = replyMessage;
      contactMessages[messageIndex].repliedAt = new Date();
      contactMessages[messageIndex].repliedBy = req.user.id;
      contactMessages[messageIndex].status = 'resolved';

      res.status(200).json({
        success: true,
        message: 'Reply sent successfully',
        data: { message: contactMessages[messageIndex] }
      });

    } catch (emailError) {
      console.error('Failed to send reply email:', emailError);
      res.status(500).json({
        success: false,
        message: 'Failed to send reply email'
      });
    }

  } catch (error) {
    console.error('Reply to message error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while sending reply'
    });
  }
});

module.exports = router;
