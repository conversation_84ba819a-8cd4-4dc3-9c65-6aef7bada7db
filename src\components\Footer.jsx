import React from 'react';
import { Phone, Mail, MapPin, Instagram, Facebook, Twitter, Leaf } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-green text-white">
      <div className="container-custom py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div>
            <div className="flex items-center mb-4">
              <Leaf className="h-8 w-8 text-mustard" />
              <span className="ml-2 font-bold text-xl">Vastavik</span>
            </div>
            <p className="mb-6">
              Pure, traditional mustard oil crafted with centuries of village expertise.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="hover:text-mustard transition-colors">
                <Facebook className="h-6 w-6" />
              </a>
              <a href="#" className="hover:text-mustard transition-colors">
                <Instagram className="h-6 w-6" />
              </a>
              <a href="#" className="hover:text-mustard transition-colors">
                <Twitter className="h-6 w-6" />
              </a>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              {['Home', 'Products', 'Benefits', 'Testimonials', 'Story'].map((item) => (
                <li key={item}>
                  <a
                    href={`#${item.toLowerCase()}`}
                    className="hover:text-mustard transition-colors"
                  >
                    {item}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4">Contact Us</h3>
            <ul className="space-y-4">
              <li className="flex items-start">
                <Phone className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
                <span>+91 8979799769</span>
              </li>
              <li className="flex items-start">
                <Mail className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
                <span><EMAIL></span>
              </li>
              <li className="flex items-start">
                <MapPin className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
                <span>Sasni, District Hathras, Uttar Pradesh, India</span>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4">WhatsApp Support</h3>
            <p className="mb-4">
              Got questions? Reach out to us directly on WhatsApp for immediate assistance.
            </p>
            <a
              href="https://wa.me/************"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-block px-6 py-3 bg-[#25D366] text-white font-semibold rounded-md shadow-md hover:bg-opacity-90 transition-colors"
            >
              Chat with Us
            </a>
          </div>
        </div>

        <div className="border-t border-white/20 mt-12 pt-6 text-center">
          <p>© {new Date().getFullYear()} Vastavik Mustard Oil. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;