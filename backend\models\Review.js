const mongoose = require('mongoose');

const reviewSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  product: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: true
  },
  order: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Order',
    required: true
  },
  rating: {
    type: Number,
    required: [true, 'Please provide a rating'],
    min: 1,
    max: 5
  },
  title: {
    type: String,
    trim: true,
    maxlength: [100, 'Review title cannot exceed 100 characters']
  },
  comment: {
    type: String,
    required: [true, 'Please provide a review comment'],
    trim: true,
    maxlength: [1000, 'Review comment cannot exceed 1000 characters']
  },
  images: [{
    public_id: String,
    url: String,
    alt: String
  }],
  pros: [{
    type: String,
    trim: true
  }],
  cons: [{
    type: String,
    trim: true
  }],
  verified: {
    type: Boolean,
    default: false
  },
  helpful: {
    count: {
      type: Number,
      default: 0
    },
    users: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }]
  },
  reported: {
    count: {
      type: Number,
      default: 0
    },
    users: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }],
    reasons: [{
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      reason: {
        type: String,
        enum: ['spam', 'inappropriate', 'fake', 'offensive', 'other']
      },
      description: String,
      reportedAt: {
        type: Date,
        default: Date.now
      }
    }]
  },
  adminResponse: {
    message: String,
    respondedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    respondedAt: Date
  },
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected', 'hidden'],
    default: 'pending'
  },
  moderationNotes: String,
  isEdited: {
    type: Boolean,
    default: false
  },
  editHistory: [{
    field: String,
    oldValue: String,
    newValue: String,
    editedAt: {
      type: Date,
      default: Date.now
    }
  }]
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Compound index to ensure one review per user per product per order
reviewSchema.index({ user: 1, product: 1, order: 1 }, { unique: true });

// Other indexes
reviewSchema.index({ product: 1, status: 1, createdAt: -1 });
reviewSchema.index({ user: 1, createdAt: -1 });
reviewSchema.index({ rating: -1 });
reviewSchema.index({ verified: 1 });

// Virtual for helpful percentage
reviewSchema.virtual('helpfulPercentage').get(function() {
  const totalVotes = this.helpful.count + this.reported.count;
  if (totalVotes === 0) return 0;
  return Math.round((this.helpful.count / totalVotes) * 100);
});

// Virtual for review age
reviewSchema.virtual('reviewAge').get(function() {
  const now = new Date();
  const diffTime = Math.abs(now - this.createdAt);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays === 1) return '1 day ago';
  if (diffDays < 30) return `${diffDays} days ago`;
  if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
  return `${Math.floor(diffDays / 365)} years ago`;
});

// Pre-save middleware to verify purchase
reviewSchema.pre('save', async function(next) {
  if (this.isNew) {
    const Order = mongoose.model('Order');
    const order = await Order.findOne({
      _id: this.order,
      user: this.user,
      'items.product': this.product,
      status: 'delivered'
    });

    if (order) {
      this.verified = true;
    }
  }
  next();
});

// Post-save middleware to update product ratings
reviewSchema.post('save', async function() {
  const Product = mongoose.model('Product');
  const product = await Product.findById(this.product);
  if (product) {
    await product.updateRatings();
  }
});

// Post-remove middleware to update product ratings
reviewSchema.post('remove', async function() {
  const Product = mongoose.model('Product');
  const product = await Product.findById(this.product);
  if (product) {
    await product.updateRatings();
  }
});

// Method to mark as helpful
reviewSchema.methods.markHelpful = async function(userId) {
  if (this.helpful.users.includes(userId)) {
    return { success: false, message: 'Already marked as helpful' };
  }

  // Remove from reported if exists
  this.reported.users = this.reported.users.filter(id => !id.equals(userId));
  if (this.reported.count > 0) {
    this.reported.count--;
  }

  // Add to helpful
  this.helpful.users.push(userId);
  this.helpful.count++;

  await this.save();
  return { success: true, message: 'Marked as helpful' };
};

// Method to report review
reviewSchema.methods.reportReview = async function(userId, reason, description) {
  if (this.reported.users.includes(userId)) {
    return { success: false, message: 'Already reported' };
  }

  // Remove from helpful if exists
  this.helpful.users = this.helpful.users.filter(id => !id.equals(userId));
  if (this.helpful.count > 0) {
    this.helpful.count--;
  }

  // Add to reported
  this.reported.users.push(userId);
  this.reported.count++;
  this.reported.reasons.push({
    user: userId,
    reason,
    description
  });

  await this.save();
  return { success: true, message: 'Review reported' };
};

// Method to add admin response
reviewSchema.methods.addAdminResponse = async function(message, adminId) {
  this.adminResponse = {
    message,
    respondedBy: adminId,
    respondedAt: new Date()
  };

  await this.save();
  return this;
};

// Method to update status
reviewSchema.methods.updateStatus = async function(status, moderationNotes) {
  this.status = status;
  if (moderationNotes) {
    this.moderationNotes = moderationNotes;
  }

  await this.save();
  return this;
};

// Static method to get product reviews with pagination
reviewSchema.statics.getProductReviews = function(productId, options = {}) {
  const {
    page = 1,
    limit = 10,
    sortBy = 'newest',
    rating = null,
    verified = null
  } = options;

  let query = { product: productId, status: 'approved' };
  
  if (rating) {
    query.rating = rating;
  }
  
  if (verified !== null) {
    query.verified = verified;
  }

  let sortOptions = {};
  switch (sortBy) {
    case 'oldest':
      sortOptions.createdAt = 1;
      break;
    case 'rating-high':
      sortOptions.rating = -1;
      break;
    case 'rating-low':
      sortOptions.rating = 1;
      break;
    case 'helpful':
      sortOptions['helpful.count'] = -1;
      break;
    default:
      sortOptions.createdAt = -1;
  }

  const skip = (page - 1) * limit;

  return this.find(query)
    .populate('user', 'name avatar')
    .sort(sortOptions)
    .skip(skip)
    .limit(limit);
};

// Static method to get review statistics
reviewSchema.statics.getReviewStats = async function(productId) {
  const stats = await this.aggregate([
    { $match: { product: mongoose.Types.ObjectId(productId), status: 'approved' } },
    {
      $group: {
        _id: '$rating',
        count: { $sum: 1 }
      }
    },
    { $sort: { _id: -1 } }
  ]);

  const totalReviews = stats.reduce((sum, stat) => sum + stat.count, 0);
  const averageRating = stats.reduce((sum, stat) => sum + (stat._id * stat.count), 0) / totalReviews;

  const ratingDistribution = {
    5: 0, 4: 0, 3: 0, 2: 0, 1: 0
  };

  stats.forEach(stat => {
    ratingDistribution[stat._id] = stat.count;
  });

  return {
    totalReviews,
    averageRating: Math.round(averageRating * 10) / 10,
    ratingDistribution
  };
};

module.exports = mongoose.model('Review', reviewSchema);
