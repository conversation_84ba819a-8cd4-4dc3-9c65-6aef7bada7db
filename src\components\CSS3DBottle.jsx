import React, { useEffect, useState } from 'react';

const CSS3DBottle = () => {
  const [scrollY, setScrollY] = useState(0);
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      setScrollY(currentScrollY);

      // Hide bottle after scrolling too much
      setIsVisible(currentScrollY < 2000);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  if (!isVisible) return null;

  // Calculate animation values based on scroll
  const rotateY = (scrollY * 0.5) % 360;
  const rotateX = Math.sin(scrollY * 0.01) * 15;
  const translateY = Math.sin(scrollY * 0.005) * 20;
  const translateX = Math.cos(scrollY * 0.003) * 10;
  const scale = 1 + Math.sin(scrollY * 0.002) * 0.2;
  const oilLevel = 70 + Math.sin(scrollY * 0.01) * 20;

  return (
    <div className="fixed top-0 right-0 w-1/2 h-screen pointer-events-none z-10 flex items-center justify-center">
      {/* 3D Bottle Container */}
      <div
        className="relative transition-all duration-100 ease-out"
        style={{
          transform: `
            perspective(1000px)
            rotateY(${rotateY}deg)
            rotateX(${rotateX}deg)
            translateY(${translateY}px)
            translateX(${translateX}px)
            scale(${scale})
          `,
          transformStyle: 'preserve-3d'
        }}
      >
        {/* Bottle Shadow */}
        <div
          className="absolute w-32 h-48 bg-black/20 rounded-lg blur-xl"
          style={{
            transform: 'translateZ(-50px) translateY(20px) rotateX(90deg)',
            transformOrigin: 'center bottom'
          }}
        ></div>

        {/* Main Bottle Body - More Rounded */}
        <div
          className="relative w-32 h-48 bg-gradient-to-b from-green-600 to-green-800 shadow-2xl"
          style={{
            background: `linear-gradient(135deg,
              rgba(43, 88, 12, 0.9) 0%,
              rgba(43, 88, 12, 0.7) 50%,
              rgba(43, 88, 12, 0.9) 100%)`,
            boxShadow: `
              inset 5px 5px 10px rgba(255, 255, 255, 0.2),
              inset -5px -5px 10px rgba(0, 0, 0, 0.3),
              0 20px 40px rgba(0, 0, 0, 0.4)
            `,
            transformStyle: 'preserve-3d',
            borderRadius: '25px 25px 30px 30px'
          }}
        >
          {/* Bottle Neck */}
          <div
            className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-3 w-8 h-8 bg-green-700 rounded-t-lg"
            style={{
              background: 'linear-gradient(135deg, #2B580C, #1a3507)',
              boxShadow: 'inset 2px 2px 4px rgba(255, 255, 255, 0.2)'
            }}
          ></div>

          {/* Bottle Cap */}
          <div
            className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-6 w-10 h-4 bg-mustard rounded-lg"
            style={{
              background: 'linear-gradient(135deg, #E3B505, #b8940a)',
              boxShadow: `
                inset 2px 2px 4px rgba(255, 255, 255, 0.3),
                0 4px 8px rgba(0, 0, 0, 0.3)
              `,
              transform: `translateZ(10px) rotateY(${rotateY * 0.5}deg)`
            }}
          ></div>

          {/* Oil Inside Bottle */}
          <div
            className="absolute bottom-2 left-2 right-2 bg-gradient-to-t from-yellow-400 to-yellow-300 transition-all duration-300"
            style={{
              height: `${oilLevel}%`,
              background: `linear-gradient(to top,
                #FFD700 0%,
                #FFA500 50%,
                #FFD700 100%)`,
              boxShadow: 'inset 0 2px 4px rgba(0, 0, 0, 0.2)',
              transform: 'translateZ(-5px)',
              borderRadius: '20px 20px 25px 25px'
            }}
          >
            {/* Oil Surface Animation */}
            <div
              className="absolute top-0 left-0 right-0 h-2 bg-gradient-to-r from-yellow-300 via-yellow-400 to-yellow-300 rounded-full"
              style={{
                animation: 'wave 2s ease-in-out infinite',
                transform: `translateY(-1px) rotateX(${Math.sin(scrollY * 0.02) * 10}deg)`
              }}
            ></div>
          </div>

          {/* Label - Fixed Size and Position */}
          <div
            className="absolute top-8 left-1/2 transform -translate-x-1/2 w-24 h-16 bg-cream rounded-md shadow-lg overflow-hidden flex flex-col items-center justify-center"
            style={{
              transform: `translateZ(5px) rotateY(${Math.sin(scrollY * 0.01) * 5}deg)`,
              background: 'linear-gradient(135deg, #FFF8E8, #F5F5DC)',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)'
            }}
          >
            {/* Vastvik Text */}
            <div className="text-xs font-bold text-green-800 mb-1">
              VASTVIK
            </div>

            {/* Pure Oil Text */}
            <div className="text-xs text-mustard font-semibold mb-1">
              Pure Oil
            </div>

            {/* Hindi Text */}
            <div className="text-xs text-green-700">
              शुद्ध तेल
            </div>
          </div>

          {/* Bottle Highlights */}
          <div
            className="absolute top-4 left-2 w-2 h-8 bg-white/30 rounded-full blur-sm"
            style={{
              transform: 'translateZ(10px)',
              background: 'linear-gradient(to bottom, rgba(255, 255, 255, 0.4), transparent)'
            }}
          ></div>
        </div>

        {/* Floating Particles */}
        {[...Array(8)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-mustard rounded-full"
            style={{
              top: `${20 + i * 10}%`,
              left: `${10 + (i % 3) * 30}%`,
              transform: `
                translateZ(${20 + i * 10}px)
                rotateY(${rotateY + i * 45}deg)
                translateX(${Math.sin(scrollY * 0.01 + i) * 20}px)
                translateY(${Math.cos(scrollY * 0.01 + i) * 15}px)
              `,
              animation: `float ${2 + i * 0.5}s ease-in-out infinite`,
              animationDelay: `${i * 0.2}s`
            }}
          ></div>
        ))}
      </div>

      {/* Removed info cards for cleaner look */}

      {/* CSS Animations */}
      <style jsx>{`
        @keyframes wave {
          0%, 100% { transform: translateY(-1px) scaleX(1); }
          50% { transform: translateY(-3px) scaleX(1.1); }
        }

        @keyframes float {
          0%, 100% { opacity: 0.6; transform: translateY(0px); }
          50% { opacity: 1; transform: translateY(-10px); }
        }
      `}</style>
    </div>
  );
};

export default CSS3DBottle;
