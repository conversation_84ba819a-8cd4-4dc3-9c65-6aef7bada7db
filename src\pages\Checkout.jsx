import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft, CreditCard, Truck, Shield, CheckCircle } from 'lucide-react';

const Checkout = () => {
  const [cartItems, setCartItems] = useState([]);
  const [step, setStep] = useState(1);
  const [formData, setFormData] = useState({
    // Shipping Information
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    state: '',
    pincode: '',
    
    // Payment Information
    paymentMethod: 'cod',
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardName: ''
  });

  useEffect(() => {
    const savedCart = localStorage.getItem('vastvik-cart');
    if (savedCart) {
      setCartItems(JSON.parse(savedCart));
    }
  }, []);

  const getTotalPrice = () => {
    return cartItems.reduce((total, item) => {
      const price = parseInt(item.price.replace('₹', ''));
      return total + (price * item.quantity);
    }, 0);
  };

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (step < 3) {
      setStep(step + 1);
    } else {
      // Process order
      localStorage.removeItem('vastvik-cart');
      setStep(4);
    }
  };

  if (cartItems.length === 0 && step !== 4) {
    return (
      <div className="min-h-screen bg-cream pt-24">
        <div className="container-custom text-center py-16">
          <h1 className="text-3xl font-bold text-brown mb-4">Your cart is empty</h1>
          <Link to="/products" className="btn-primary">Continue Shopping</Link>
        </div>
      </div>
    );
  }

  if (step === 4) {
    return (
      <div className="min-h-screen bg-cream pt-24">
        <div className="container-custom">
          <div className="max-w-2xl mx-auto text-center py-16">
            <CheckCircle className="h-24 w-24 text-green mx-auto mb-6" />
            <h1 className="text-3xl font-bold text-brown mb-4">Order Confirmed!</h1>
            <p className="text-brown/70 mb-8">
              Thank you for your order. We'll send you a confirmation email shortly.
            </p>
            <div className="bg-white p-6 rounded-lg shadow-md mb-8">
              <h3 className="font-semibold text-brown mb-4">Order Details</h3>
              <div className="text-left space-y-2">
                <div className="flex justify-between">
                  <span>Order Number:</span>
                  <span className="font-semibold">#VAS{Date.now().toString().slice(-6)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Total Amount:</span>
                  <span className="font-semibold">₹{getTotalPrice()}</span>
                </div>
                <div className="flex justify-between">
                  <span>Payment Method:</span>
                  <span className="font-semibold">
                    {formData.paymentMethod === 'cod' ? 'Cash on Delivery' : 'Card Payment'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Estimated Delivery:</span>
                  <span className="font-semibold">3-5 business days</span>
                </div>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/products" className="btn-primary">Continue Shopping</Link>
              <Link to="/account" className="btn-secondary">Track Order</Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-cream pt-24">
      <div className="container-custom">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-brown mb-2">Checkout</h1>
            <div className="flex items-center space-x-4 text-sm">
              <span className={`${step >= 1 ? 'text-green' : 'text-brown/50'}`}>
                1. Shipping
              </span>
              <span className={`${step >= 2 ? 'text-green' : 'text-brown/50'}`}>
                2. Payment
              </span>
              <span className={`${step >= 3 ? 'text-green' : 'text-brown/50'}`}>
                3. Review
              </span>
            </div>
          </div>
          <Link 
            to="/cart"
            className="text-green hover:text-green-dark transition-colors flex items-center space-x-2"
          >
            <ArrowLeft className="h-5 w-5" />
            <span>Back to Cart</span>
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Form */}
          <div className="lg:col-span-2">
            <form onSubmit={handleSubmit} className="bg-white rounded-lg p-8 shadow-md">
              {step === 1 && (
                <div>
                  <h2 className="text-xl font-bold text-brown mb-6">Shipping Information</h2>
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-brown font-medium mb-2">First Name *</label>
                        <input
                          type="text"
                          name="firstName"
                          value={formData.firstName}
                          onChange={handleInputChange}
                          required
                          className="w-full px-4 py-3 border border-brown/20 rounded-md focus:outline-none focus:border-mustard"
                        />
                      </div>
                      <div>
                        <label className="block text-brown font-medium mb-2">Last Name *</label>
                        <input
                          type="text"
                          name="lastName"
                          value={formData.lastName}
                          onChange={handleInputChange}
                          required
                          className="w-full px-4 py-3 border border-brown/20 rounded-md focus:outline-none focus:border-mustard"
                        />
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-brown font-medium mb-2">Email *</label>
                        <input
                          type="email"
                          name="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          required
                          className="w-full px-4 py-3 border border-brown/20 rounded-md focus:outline-none focus:border-mustard"
                        />
                      </div>
                      <div>
                        <label className="block text-brown font-medium mb-2">Phone *</label>
                        <input
                          type="tel"
                          name="phone"
                          value={formData.phone}
                          onChange={handleInputChange}
                          required
                          className="w-full px-4 py-3 border border-brown/20 rounded-md focus:outline-none focus:border-mustard"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-brown font-medium mb-2">Address *</label>
                      <input
                        type="text"
                        name="address"
                        value={formData.address}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-brown/20 rounded-md focus:outline-none focus:border-mustard"
                      />
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-brown font-medium mb-2">City *</label>
                        <input
                          type="text"
                          name="city"
                          value={formData.city}
                          onChange={handleInputChange}
                          required
                          className="w-full px-4 py-3 border border-brown/20 rounded-md focus:outline-none focus:border-mustard"
                        />
                      </div>
                      <div>
                        <label className="block text-brown font-medium mb-2">State *</label>
                        <input
                          type="text"
                          name="state"
                          value={formData.state}
                          onChange={handleInputChange}
                          required
                          className="w-full px-4 py-3 border border-brown/20 rounded-md focus:outline-none focus:border-mustard"
                        />
                      </div>
                      <div>
                        <label className="block text-brown font-medium mb-2">PIN Code *</label>
                        <input
                          type="text"
                          name="pincode"
                          value={formData.pincode}
                          onChange={handleInputChange}
                          required
                          className="w-full px-4 py-3 border border-brown/20 rounded-md focus:outline-none focus:border-mustard"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {step === 2 && (
                <div>
                  <h2 className="text-xl font-bold text-brown mb-6">Payment Method</h2>
                  <div className="space-y-6">
                    <div className="space-y-4">
                      <label className="flex items-center p-4 border border-brown/20 rounded-md cursor-pointer hover:bg-cream">
                        <input
                          type="radio"
                          name="paymentMethod"
                          value="cod"
                          checked={formData.paymentMethod === 'cod'}
                          onChange={handleInputChange}
                          className="mr-3"
                        />
                        <Truck className="h-6 w-6 text-green mr-3" />
                        <div>
                          <div className="font-semibold text-brown">Cash on Delivery</div>
                          <div className="text-brown/70 text-sm">Pay when you receive your order</div>
                        </div>
                      </label>
                      
                      <label className="flex items-center p-4 border border-brown/20 rounded-md cursor-pointer hover:bg-cream">
                        <input
                          type="radio"
                          name="paymentMethod"
                          value="card"
                          checked={formData.paymentMethod === 'card'}
                          onChange={handleInputChange}
                          className="mr-3"
                        />
                        <CreditCard className="h-6 w-6 text-green mr-3" />
                        <div>
                          <div className="font-semibold text-brown">Credit/Debit Card</div>
                          <div className="text-brown/70 text-sm">Secure online payment</div>
                        </div>
                      </label>
                    </div>
                    
                    {formData.paymentMethod === 'card' && (
                      <div className="space-y-4 p-4 bg-cream rounded-md">
                        <div>
                          <label className="block text-brown font-medium mb-2">Card Number</label>
                          <input
                            type="text"
                            name="cardNumber"
                            value={formData.cardNumber}
                            onChange={handleInputChange}
                            placeholder="1234 5678 9012 3456"
                            className="w-full px-4 py-3 border border-brown/20 rounded-md focus:outline-none focus:border-mustard"
                          />
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="block text-brown font-medium mb-2">Expiry Date</label>
                            <input
                              type="text"
                              name="expiryDate"
                              value={formData.expiryDate}
                              onChange={handleInputChange}
                              placeholder="MM/YY"
                              className="w-full px-4 py-3 border border-brown/20 rounded-md focus:outline-none focus:border-mustard"
                            />
                          </div>
                          <div>
                            <label className="block text-brown font-medium mb-2">CVV</label>
                            <input
                              type="text"
                              name="cvv"
                              value={formData.cvv}
                              onChange={handleInputChange}
                              placeholder="123"
                              className="w-full px-4 py-3 border border-brown/20 rounded-md focus:outline-none focus:border-mustard"
                            />
                          </div>
                        </div>
                        
                        <div>
                          <label className="block text-brown font-medium mb-2">Cardholder Name</label>
                          <input
                            type="text"
                            name="cardName"
                            value={formData.cardName}
                            onChange={handleInputChange}
                            placeholder="Name on card"
                            className="w-full px-4 py-3 border border-brown/20 rounded-md focus:outline-none focus:border-mustard"
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {step === 3 && (
                <div>
                  <h2 className="text-xl font-bold text-brown mb-6">Review Your Order</h2>
                  <div className="space-y-6">
                    <div>
                      <h3 className="font-semibold text-brown mb-3">Shipping Address</h3>
                      <div className="p-4 bg-cream rounded-md text-brown/70">
                        <p>{formData.firstName} {formData.lastName}</p>
                        <p>{formData.address}</p>
                        <p>{formData.city}, {formData.state} {formData.pincode}</p>
                        <p>{formData.phone}</p>
                      </div>
                    </div>
                    
                    <div>
                      <h3 className="font-semibold text-brown mb-3">Payment Method</h3>
                      <div className="p-4 bg-cream rounded-md text-brown/70">
                        {formData.paymentMethod === 'cod' ? 'Cash on Delivery' : 'Credit/Debit Card'}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              <div className="mt-8 flex justify-between">
                {step > 1 && (
                  <button
                    type="button"
                    onClick={() => setStep(step - 1)}
                    className="px-6 py-3 border border-brown text-brown rounded-md hover:bg-cream transition-colors"
                  >
                    Previous
                  </button>
                )}
                <button
                  type="submit"
                  className="btn-primary ml-auto"
                >
                  {step === 3 ? 'Place Order' : 'Continue'}
                </button>
              </div>
            </form>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg p-6 shadow-md sticky top-24">
              <h2 className="text-xl font-bold text-brown mb-6">Order Summary</h2>
              
              <div className="space-y-4 mb-6">
                {cartItems.map((item) => (
                  <div key={item.id} className="flex items-center space-x-3">
                    <img 
                      src={item.image} 
                      alt={item.name}
                      className="w-12 h-12 object-cover rounded"
                    />
                    <div className="flex-1">
                      <h4 className="font-medium text-brown text-sm">{item.name}</h4>
                      <p className="text-brown/70 text-sm">{item.size} × {item.quantity}</p>
                    </div>
                    <span className="font-semibold text-brown">
                      ₹{parseInt(item.price.replace('₹', '')) * item.quantity}
                    </span>
                  </div>
                ))}
              </div>
              
              <div className="space-y-3 border-t border-brown/20 pt-4">
                <div className="flex justify-between">
                  <span className="text-brown/70">Subtotal</span>
                  <span className="font-semibold text-brown">₹{getTotalPrice()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-brown/70">Shipping</span>
                  <span className="font-semibold text-green">Free</span>
                </div>
                <div className="flex justify-between text-lg font-bold">
                  <span className="text-brown">Total</span>
                  <span className="text-brown">₹{getTotalPrice()}</span>
                </div>
              </div>
              
              <div className="mt-6 space-y-3 text-sm text-brown/70">
                <div className="flex items-center space-x-2">
                  <Shield className="h-4 w-4" />
                  <span>Secure checkout</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Truck className="h-4 w-4" />
                  <span>Free delivery</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4" />
                  <span>100% satisfaction guarantee</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Checkout;
