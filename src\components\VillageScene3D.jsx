import React, { useRef, useMemo, Suspense } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Text, Float, Environment } from '@react-three/drei';
import * as THREE from 'three';

// 3D Mustard Field Component
function MustardField() {
  const meshRef = useRef();
  const instancedMeshRef = useRef();

  const { positions, colors } = useMemo(() => {
    const positions = [];
    const colors = [];
    const color = new THREE.Color();

    for (let i = 0; i < 1000; i++) {
      positions.push(
        (Math.random() - 0.5) * 20,
        Math.random() * 2,
        (Math.random() - 0.5) * 20
      );

      color.setHSL(0.15 + Math.random() * 0.1, 0.8, 0.6);
      colors.push(color.r, color.g, color.b);
    }

    return { positions: new Float32Array(positions), colors: new Float32Array(colors) };
  }, []);

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1;
    }
  });

  return (
    <points ref={meshRef}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={positions.length / 3}
          array={positions}
          itemSize={3}
        />
        <bufferAttribute
          attach="attributes-color"
          count={colors.length / 3}
          array={colors}
          itemSize={3}
        />
      </bufferGeometry>
      <pointsMaterial size={0.1} vertexColors />
    </points>
  );
}

// 3D Oil Bottle Component
function OilBottle({ position = [0, 0, 0] }) {
  const meshRef = useRef();

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime) * 0.3;
      meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 2) * 0.1;
    }
  });

  return (
    <Float speed={1.5} rotationIntensity={0.5} floatIntensity={0.5}>
      <group ref={meshRef} position={position}>
        {/* Bottle Body */}
        <mesh position={[0, 0, 0]}>
          <cylinderGeometry args={[0.3, 0.4, 1.5, 8]} />
          <meshStandardMaterial color="#2B580C" transparent opacity={0.8} />
        </mesh>

        {/* Bottle Neck */}
        <mesh position={[0, 0.9, 0]}>
          <cylinderGeometry args={[0.15, 0.2, 0.3, 8]} />
          <meshStandardMaterial color="#2B580C" />
        </mesh>

        {/* Cap */}
        <mesh position={[0, 1.15, 0]}>
          <cylinderGeometry args={[0.18, 0.18, 0.1, 8]} />
          <meshStandardMaterial color="#E3B505" />
        </mesh>

        {/* Label */}
        <mesh position={[0, 0.2, 0.31]}>
          <planeGeometry args={[0.5, 0.8]} />
          <meshStandardMaterial color="#FFF8E8" />
        </mesh>
      </group>
    </Float>
  );
}

// Village House Component
function VillageHouse({ position = [0, 0, 0] }) {
  return (
    <group position={position}>
      {/* House Base */}
      <mesh position={[0, 0.5, 0]}>
        <boxGeometry args={[2, 1, 1.5]} />
        <meshStandardMaterial color="#D2B48C" />
      </mesh>

      {/* Roof */}
      <mesh position={[0, 1.2, 0]} rotation={[0, 0, 0]}>
        <coneGeometry args={[1.5, 0.8, 4]} />
        <meshStandardMaterial color="#8B4513" />
      </mesh>

      {/* Door */}
      <mesh position={[0, 0.3, 0.76]}>
        <boxGeometry args={[0.4, 0.6, 0.05]} />
        <meshStandardMaterial color="#654321" />
      </mesh>

      {/* Windows */}
      <mesh position={[-0.5, 0.6, 0.76]}>
        <boxGeometry args={[0.3, 0.3, 0.05]} />
        <meshStandardMaterial color="#87CEEB" />
      </mesh>
      <mesh position={[0.5, 0.6, 0.76]}>
        <boxGeometry args={[0.3, 0.3, 0.05]} />
        <meshStandardMaterial color="#87CEEB" />
      </mesh>
    </group>
  );
}

// Animated Tree Component
function Tree({ position = [0, 0, 0] }) {
  const treeRef = useRef();

  useFrame((state) => {
    if (treeRef.current) {
      treeRef.current.rotation.z = Math.sin(state.clock.elapsedTime * 2) * 0.1;
    }
  });

  return (
    <group ref={treeRef} position={position}>
      {/* Trunk */}
      <mesh position={[0, 0.5, 0]}>
        <cylinderGeometry args={[0.1, 0.15, 1, 8]} />
        <meshStandardMaterial color="#8B4513" />
      </mesh>

      {/* Leaves */}
      <mesh position={[0, 1.2, 0]}>
        <sphereGeometry args={[0.6, 8, 6]} />
        <meshStandardMaterial color="#228B22" />
      </mesh>
    </group>
  );
}

// Main Village Scene
function VillageScene() {
  return (
    <>
      {/* Lighting */}
      <ambientLight intensity={0.6} />
      <directionalLight position={[10, 10, 5]} intensity={1} castShadow />
      <pointLight position={[0, 5, 0]} intensity={0.5} color="#E3B505" />

      {/* Environment */}
      <Environment preset="sunset" />

      {/* Ground */}
      <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -1, 0]} receiveShadow>
        <planeGeometry args={[50, 50]} />
        <meshStandardMaterial color="#90EE90" />
      </mesh>

      {/* Mustard Field */}
      <MustardField />

      {/* Oil Bottles */}
      <OilBottle position={[2, 1, 2]} />
      <OilBottle position={[-2, 1, -1]} />
      <OilBottle position={[0, 1, -3]} />

      {/* Village Houses */}
      <VillageHouse position={[-5, 0, -5]} />
      <VillageHouse position={[5, 0, -6]} />
      <VillageHouse position={[-3, 0, -8]} />

      {/* Trees */}
      <Tree position={[3, 0, 4]} />
      <Tree position={[-4, 0, 3]} />
      <Tree position={[6, 0, -2]} />
      <Tree position={[-6, 0, -1]} />

      {/* Simple Cloud Shapes */}
      <mesh position={[-10, 8, -10]}>
        <sphereGeometry args={[2, 8, 6]} />
        <meshStandardMaterial color="white" transparent opacity={0.3} />
      </mesh>
      <mesh position={[10, 6, -8]}>
        <sphereGeometry args={[1.5, 8, 6]} />
        <meshStandardMaterial color="white" transparent opacity={0.4} />
      </mesh>

      {/* 3D Text */}
      <Float speed={2} rotationIntensity={0.3} floatIntensity={0.8}>
        <Text
          position={[0, 3, 0]}
          fontSize={1}
          color="#E3B505"
          anchorX="center"
          anchorY="middle"
        >
          Vastvik
        </Text>
      </Float>

      <Float speed={1.5} rotationIntensity={0.2} floatIntensity={0.5}>
        <Text
          position={[0, 2, 0]}
          fontSize={0.5}
          color="#2B580C"
          anchorX="center"
          anchorY="middle"
        >
          Pure Village Oil
        </Text>
      </Float>

      {/* Controls */}
      <OrbitControls
        enablePan={false}
        enableZoom={true}
        enableRotate={true}
        minDistance={5}
        maxDistance={20}
        minPolarAngle={Math.PI / 6}
        maxPolarAngle={Math.PI / 2}
        autoRotate={true}
        autoRotateSpeed={0.5}
      />
    </>
  );
}

// Loading Component
function LoadingFallback() {
  return (
    <div className="flex items-center justify-center h-full">
      <div className="text-center">
        <div className="w-16 h-16 border-4 border-mustard border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <p className="text-brown font-medium">Loading Village Scene...</p>
      </div>
    </div>
  );
}

// Main Component
const VillageScene3D = () => {
  return (
    <div className="w-full h-screen relative">
      <Canvas
        camera={{ position: [0, 5, 10], fov: 60 }}
        shadows
        className="bg-gradient-to-b from-sky-200 to-green-100"
      >
        <Suspense fallback={null}>
          <VillageScene />
        </Suspense>
      </Canvas>

      {/* Overlay Content */}
      <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
        <div className="text-center text-white z-10 bg-black/20 p-8 rounded-lg backdrop-blur-sm">
          <h1 className="text-4xl md:text-6xl font-bold mb-4 text-shadow-lg">
            Welcome to Vastvik
          </h1>
          <p className="text-xl md:text-2xl mb-8 text-shadow">
            Experience Pure Village Tradition
          </p>
          <div className="space-x-4 pointer-events-auto">
            <button className="btn-primary text-lg px-8 py-4">
              Explore Products
            </button>
            <button className="btn-secondary text-lg px-8 py-4">
              Our Story
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VillageScene3D;
