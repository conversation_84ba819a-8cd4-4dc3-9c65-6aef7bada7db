import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>rk<PERSON> } from 'lucide-react';

const Features3D = () => {
  const features = [
    {
      icon: <Cube className="h-8 w-8" />,
      title: "3D Village Scene",
      description: "Interactive 3D environment with floating oil bottles, village houses, and animated elements",
      demo: "Rotate, zoom, and explore the virtual village"
    },
    {
      icon: <Palette className="h-8 w-8" />,
      title: "Village Aesthetics",
      description: "Traditional patterns, mustard field animations, and authentic village color schemes",
      demo: "Hover over cards to see 3D effects"
    },
    {
      icon: <MousePointer className="h-8 w-8" />,
      title: "Interactive Elements",
      description: "Clickable hotspots, floating particles, and responsive 3D transformations",
      demo: "Click control buttons to switch views"
    },
    {
      icon: <Zap className="h-8 w-8" />,
      title: "Smooth Animations",
      description: "CSS3 and Three.js powered animations with village-themed movements",
      demo: "Watch elements float and pulse naturally"
    },
    {
      icon: <Eye className="h-8 w-8" />,
      title: "Multiple Views",
      description: "Switch between 3D scene, traditional view, and fallback options",
      demo: "Use control panel to toggle views"
    },
    {
      icon: <Sparkles className="h-8 w-8" />,
      title: "Enhanced UX",
      description: "Loading states, error handling, and progressive enhancement",
      demo: "Graceful fallbacks for all devices"
    }
  ];

  return (
    <section className="section-padding bg-white">
      <div className="container-custom">
        <h2 className="section-title">3D Village Experience</h2>
        <p className="section-subtitle">
          Immerse yourself in our interactive 3D village environment that brings 
          the authentic mustard oil production process to life.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div 
              key={index} 
              className="card group hover:bg-gradient-to-br hover:from-mustard/10 hover:to-green/10 transition-all duration-500 card-3d relative overflow-hidden"
            >
              {/* Icon with 3D effect */}
              <div className="mb-6 flex justify-center">
                <div className="p-4 bg-mustard/20 rounded-full group-hover:bg-mustard/30 transition-all duration-300 village-glow group-hover:scale-110 transform group-hover:rotate-12">
                  <div className="text-green group-hover:text-brown transition-colors">
                    {feature.icon}
                  </div>
                </div>
              </div>

              {/* Content */}
              <h3 className="text-xl font-semibold text-brown mb-3 group-hover:text-mustard transition-colors">
                {feature.title}
              </h3>
              
              <p className="text-brown/70 mb-4 group-hover:text-brown transition-colors">
                {feature.description}
              </p>
              
              <div className="text-sm text-green font-medium group-hover:text-green-dark transition-colors">
                💡 {feature.demo}
              </div>

              {/* 3D Floating Elements */}
              <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="w-2 h-2 bg-mustard rounded-full village-float"></div>
              </div>
              <div className="absolute bottom-4 left-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="w-1 h-1 bg-green rounded-full village-pulse"></div>
              </div>
              <div className="absolute top-1/2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                <div className="w-1 h-1 bg-brown rounded-full village-bounce"></div>
              </div>

              {/* Shimmer Effect */}
              <div className="absolute inset-0 village-shimmer opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              
              {/* 3D Border Effect */}
              <div className="absolute inset-0 border-2 border-transparent group-hover:border-mustard/20 rounded-lg transition-all duration-300"></div>
            </div>
          ))}
        </div>

        {/* Interactive Demo Section */}
        <div className="mt-16 bg-gradient-to-br from-cream to-mustard/10 rounded-2xl p-8 relative overflow-hidden">
          <div className="relative z-10">
            <h3 className="text-2xl font-bold text-brown mb-6 text-center">
              Experience the 3D Magic
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
              <div className="group">
                <div className="w-16 h-16 bg-mustard/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 village-glow">
                  <Cube className="h-8 w-8 text-green rotate-3d" />
                </div>
                <h4 className="font-semibold text-brown mb-2">3D Scene</h4>
                <p className="text-brown/70 text-sm">Interactive village with Three.js</p>
              </div>
              
              <div className="group">
                <div className="w-16 h-16 bg-green/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 village-glow">
                  <Sparkles className="h-8 w-8 text-mustard village-pulse" />
                </div>
                <h4 className="font-semibold text-brown mb-2">Animations</h4>
                <p className="text-brown/70 text-sm">Smooth village-themed effects</p>
              </div>
              
              <div className="group">
                <div className="w-16 h-16 bg-brown/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 village-glow">
                  <Eye className="h-8 w-8 text-green village-float" />
                </div>
                <h4 className="font-semibold text-brown mb-2">Responsive</h4>
                <p className="text-brown/70 text-sm">Works on all devices</p>
              </div>
            </div>
          </div>

          {/* Background Pattern */}
          <div className="absolute inset-0 village-pattern opacity-5"></div>
          
          {/* Floating Elements */}
          <div className="absolute top-8 right-8 w-3 h-3 bg-mustard/30 rounded-full village-float"></div>
          <div className="absolute bottom-8 left-8 w-2 h-2 bg-green/30 rounded-full village-pulse"></div>
          <div className="absolute top-1/2 left-8 w-1 h-1 bg-brown/30 rounded-full village-bounce"></div>
        </div>

        {/* Technology Stack */}
        <div className="mt-12 text-center">
          <h4 className="text-lg font-semibold text-brown mb-4">Powered by Modern Technology</h4>
          <div className="flex flex-wrap justify-center gap-4">
            <span className="px-4 py-2 bg-mustard/20 text-brown rounded-full text-sm font-medium">
              Three.js
            </span>
            <span className="px-4 py-2 bg-green/20 text-brown rounded-full text-sm font-medium">
              React Three Fiber
            </span>
            <span className="px-4 py-2 bg-brown/20 text-brown rounded-full text-sm font-medium">
              CSS3 Animations
            </span>
            <span className="px-4 py-2 bg-mustard/20 text-brown rounded-full text-sm font-medium">
              WebGL
            </span>
            <span className="px-4 py-2 bg-green/20 text-brown rounded-full text-sm font-medium">
              Responsive Design
            </span>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Features3D;
