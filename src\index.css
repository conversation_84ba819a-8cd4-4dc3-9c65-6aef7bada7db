@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Poppins', sans-serif;
    scroll-behavior: smooth;
  }

  body {
    @apply bg-cream text-brown;
  }
}

@layer components {
  .btn-primary {
    @apply px-6 py-3 bg-mustard text-brown font-semibold rounded-md shadow-md
    hover:bg-mustard-dark transition-all duration-300 transform hover:-translate-y-1
    hover:shadow-xl relative overflow-hidden;
  }

  .btn-primary::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent
    transform -skew-x-12 -translate-x-full transition-transform duration-700;
  }

  .btn-primary:hover::before {
    @apply translate-x-full;
  }

  .btn-secondary {
    @apply px-6 py-3 bg-green text-white font-semibold rounded-md shadow-md
    hover:bg-green-dark transition-all duration-300 transform hover:-translate-y-1
    hover:shadow-xl relative overflow-hidden;
  }

  .btn-secondary::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent
    transform -skew-x-12 -translate-x-full transition-transform duration-700;
  }

  .btn-secondary:hover::before {
    @apply translate-x-full;
  }

  .section-title {
    @apply text-3xl md:text-4xl font-bold text-brown mb-6 text-center relative;
  }

  .section-title::after {
    content: '';
    @apply absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-16 h-1 bg-mustard rounded-full;
  }

  .section-subtitle {
    @apply text-xl text-brown/80 mb-12 text-center max-w-3xl mx-auto;
  }

  .card {
    @apply bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-all duration-300
    transform hover:-translate-y-1 border border-transparent hover:border-mustard/20
    min-h-fit w-full;
  }

  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .section-padding {
    @apply py-16 md:py-24;
  }

  /* Village-themed animations */
  .village-float {
    animation: villageFloat 6s ease-in-out infinite;
  }

  .village-bounce {
    animation: villageBounce 2s ease-in-out infinite;
  }

  .village-pulse {
    animation: villagePulse 3s ease-in-out infinite;
  }

  .village-shimmer {
    position: relative;
    overflow: hidden;
  }

  .village-shimmer::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 2s infinite;
  }
}

@layer utilities {
  /* Village-themed keyframe animations */
  @keyframes villageFloat {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  @keyframes villageBounce {
    0%, 20%, 50%, 80%, 100% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-10px);
    }
    60% {
      transform: translateY(-5px);
    }
  }

  @keyframes villagePulse {
    0%, 100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.7;
      transform: scale(1.05);
    }
  }

  @keyframes shimmer {
    0% {
      left: -100%;
    }
    100% {
      left: 100%;
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fadeIn {
    animation: fadeIn 0.5s ease-out;
  }

  /* Traditional Indian patterns */
  .village-pattern {
    background-image:
      radial-gradient(circle at 25% 25%, #E3B505 2px, transparent 2px),
      radial-gradient(circle at 75% 75%, #2B580C 2px, transparent 2px);
    background-size: 20px 20px;
    background-position: 0 0, 10px 10px;
  }

  .village-border {
    border-image: repeating-linear-gradient(
      45deg,
      #E3B505,
      #E3B505 5px,
      #2B580C 5px,
      #2B580C 10px
    ) 1;
  }

  /* 3D Scene Enhancements */
  .text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  }

  .text-shadow-lg {
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.7);
  }

  .animation-delay-150 {
    animation-delay: 150ms;
  }

  .animation-delay-300 {
    animation-delay: 300ms;
  }

  /* 3D Loading Animation */
  @keyframes rotate3d {
    0% {
      transform: rotateY(0deg) rotateX(0deg);
    }
    25% {
      transform: rotateY(90deg) rotateX(0deg);
    }
    50% {
      transform: rotateY(180deg) rotateX(90deg);
    }
    75% {
      transform: rotateY(270deg) rotateX(90deg);
    }
    100% {
      transform: rotateY(360deg) rotateX(0deg);
    }
  }

  .rotate-3d {
    animation: rotate3d 3s infinite linear;
  }

  /* Village Glow Effect */
  .village-glow {
    box-shadow:
      0 0 20px rgba(227, 181, 5, 0.3),
      0 0 40px rgba(227, 181, 5, 0.2),
      0 0 60px rgba(227, 181, 5, 0.1);
  }

  /* Mustard Field Animation */
  @keyframes mustardWave {
    0%, 100% {
      transform: translateY(0px) rotate(0deg);
    }
    25% {
      transform: translateY(-5px) rotate(1deg);
    }
    50% {
      transform: translateY(-10px) rotate(0deg);
    }
    75% {
      transform: translateY(-5px) rotate(-1deg);
    }
  }

  .mustard-wave {
    animation: mustardWave 4s ease-in-out infinite;
  }

  /* 3D Card Hover Effect */
  .card-3d {
    transform-style: preserve-3d;
    transition: transform 0.3s ease;
  }

  .card-3d:hover {
    transform: rotateY(10deg) rotateX(5deg) translateZ(20px);
  }

  /* Village Smoke Animation */
  @keyframes smoke {
    0% {
      opacity: 0;
      transform: translateY(0px) scale(0.5);
    }
    50% {
      opacity: 0.8;
      transform: translateY(-20px) scale(1);
    }
    100% {
      opacity: 0;
      transform: translateY(-40px) scale(1.5);
    }
  }

  .village-smoke {
    animation: smoke 3s ease-out infinite;
  }

  /* Toast Animations */
  @keyframes slideIn {
    from {
      transform: translateX(calc(100% + 1rem));
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes progress {
    from {
      width: 100%;
    }
    to {
      width: 0%;
    }
  }

  .animate-slideIn {
    animation: slideIn 0.3s ease-out;
  }

  .animate-progress {
    animation: progress 4s linear;
  }
}