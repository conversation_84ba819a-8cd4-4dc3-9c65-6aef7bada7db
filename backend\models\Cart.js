const mongoose = require('mongoose');

const cartSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  items: [{
    product: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Product',
      required: true
    },
    quantity: {
      type: Number,
      required: true,
      min: 1,
      default: 1
    },
    price: {
      type: Number,
      required: true
    },
    discountPrice: Number,
    addedAt: {
      type: Date,
      default: Date.now
    }
  }],
  coupon: {
    code: String,
    discountType: {
      type: String,
      enum: ['percentage', 'fixed']
    },
    discountValue: Number,
    discountAmount: Number,
    appliedAt: Date
  },
  pricing: {
    subtotal: {
      type: Number,
      default: 0
    },
    discount: {
      type: Number,
      default: 0
    },
    shippingCost: {
      type: Number,
      default: 0
    },
    tax: {
      type: Number,
      default: 0
    },
    total: {
      type: Number,
      default: 0
    }
  },
  shippingAddress: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Address'
  },
  notes: String,
  isActive: {
    type: Boolean,
    default: true
  },
  expiresAt: {
    type: Date,
    default: () => new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
    index: { expireAfterSeconds: 0 }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for total items count
cartSchema.virtual('totalItems').get(function() {
  return this.items.reduce((total, item) => total + item.quantity, 0);
});

// Virtual for total weight
cartSchema.virtual('totalWeight').get(function() {
  return this.items.reduce((total, item) => {
    const weight = item.product?.weight?.value || 0;
    return total + (weight * item.quantity);
  }, 0);
});

// Indexes
cartSchema.index({ user: 1 });
cartSchema.index({ 'items.product': 1 });
cartSchema.index({ updatedAt: -1 });

// Pre-save middleware to calculate pricing
cartSchema.pre('save', function(next) {
  this.calculatePricing();
  next();
});

// Method to add item to cart
cartSchema.methods.addItem = async function(productId, quantity = 1, price, discountPrice) {
  const existingItemIndex = this.items.findIndex(
    item => item.product.toString() === productId.toString()
  );

  if (existingItemIndex > -1) {
    // Update existing item
    this.items[existingItemIndex].quantity += quantity;
    this.items[existingItemIndex].price = price;
    this.items[existingItemIndex].discountPrice = discountPrice;
  } else {
    // Add new item
    this.items.push({
      product: productId,
      quantity,
      price,
      discountPrice,
      addedAt: new Date()
    });
  }

  this.calculatePricing();
  return await this.save();
};

// Method to update item quantity
cartSchema.methods.updateItemQuantity = async function(productId, quantity) {
  const itemIndex = this.items.findIndex(
    item => item.product.toString() === productId.toString()
  );

  if (itemIndex === -1) {
    throw new Error('Item not found in cart');
  }

  if (quantity <= 0) {
    this.items.splice(itemIndex, 1);
  } else {
    this.items[itemIndex].quantity = quantity;
  }

  this.calculatePricing();
  return await this.save();
};

// Method to remove item from cart
cartSchema.methods.removeItem = async function(productId) {
  this.items = this.items.filter(
    item => item.product.toString() !== productId.toString()
  );

  this.calculatePricing();
  return await this.save();
};

// Method to clear cart
cartSchema.methods.clearCart = async function() {
  this.items = [];
  this.coupon = undefined;
  this.calculatePricing();
  return await this.save();
};

// Method to apply coupon
cartSchema.methods.applyCoupon = async function(couponCode, discountType, discountValue) {
  this.coupon = {
    code: couponCode,
    discountType,
    discountValue,
    appliedAt: new Date()
  };

  this.calculatePricing();
  return await this.save();
};

// Method to remove coupon
cartSchema.methods.removeCoupon = async function() {
  this.coupon = undefined;
  this.calculatePricing();
  return await this.save();
};

// Method to calculate pricing
cartSchema.methods.calculatePricing = function() {
  // Calculate subtotal
  this.pricing.subtotal = this.items.reduce((total, item) => {
    const itemPrice = item.discountPrice || item.price;
    return total + (itemPrice * item.quantity);
  }, 0);

  // Calculate discount from coupon
  this.pricing.discount = 0;
  if (this.coupon) {
    if (this.coupon.discountType === 'percentage') {
      this.pricing.discount = (this.pricing.subtotal * this.coupon.discountValue) / 100;
    } else if (this.coupon.discountType === 'fixed') {
      this.pricing.discount = Math.min(this.coupon.discountValue, this.pricing.subtotal);
    }
    this.coupon.discountAmount = this.pricing.discount;
  }

  // Calculate shipping cost (free shipping over ₹500)
  this.pricing.shippingCost = this.pricing.subtotal >= 500 ? 0 : 50;

  // Calculate tax (18% GST)
  const taxableAmount = this.pricing.subtotal - this.pricing.discount;
  this.pricing.tax = Math.round((taxableAmount * 0.18) * 100) / 100;

  // Calculate total
  this.pricing.total = this.pricing.subtotal - this.pricing.discount + this.pricing.shippingCost + this.pricing.tax;

  return this.pricing;
};

// Method to validate cart items
cartSchema.methods.validateItems = async function() {
  const Product = mongoose.model('Product');
  const validationErrors = [];

  for (let i = 0; i < this.items.length; i++) {
    const item = this.items[i];
    const product = await Product.findById(item.product);

    if (!product) {
      validationErrors.push({
        item: item.product,
        error: 'Product not found'
      });
      continue;
    }

    if (!product.isActive) {
      validationErrors.push({
        item: item.product,
        error: 'Product is no longer available'
      });
      continue;
    }

    if (product.stock < item.quantity) {
      validationErrors.push({
        item: item.product,
        error: `Only ${product.stock} items available in stock`
      });
      continue;
    }

    // Update price if changed
    if (item.price !== product.price || item.discountPrice !== product.discountPrice) {
      item.price = product.price;
      item.discountPrice = product.discountPrice;
    }
  }

  if (validationErrors.length === 0) {
    this.calculatePricing();
    await this.save();
  }

  return validationErrors;
};

// Static method to get or create cart for user
cartSchema.statics.getOrCreateCart = async function(userId) {
  let cart = await this.findOne({ user: userId }).populate('items.product');
  
  if (!cart) {
    cart = new this({ user: userId });
    await cart.save();
  }

  return cart;
};

// Static method to merge guest cart with user cart
cartSchema.statics.mergeGuestCart = async function(userId, guestCartItems) {
  const cart = await this.getOrCreateCart(userId);

  for (const guestItem of guestCartItems) {
    await cart.addItem(
      guestItem.product,
      guestItem.quantity,
      guestItem.price,
      guestItem.discountPrice
    );
  }

  return cart;
};

module.exports = mongoose.model('Cart', cartSchema);
