import React from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { ShoppingCart, ChevronLeft, Check } from 'lucide-react';
import { products } from '../data/products';

const ProductDetail = () => {
  const { id } = useParams();
  const product = products.find(p => p.id === parseInt(id));

  if (!product) {
    return (
      <div className="section-padding bg-cream">
        <div className="container-custom text-center">
          <h1 className="text-2xl font-bold mb-4">Product Not Found</h1>
          <Link to="/products" className="text-green hover:text-green-dark">
            Return to Products
          </Link>
        </div>
      </div>
    );
  }

  return (
    <section className="section-padding bg-cream">
      <div className="container-custom">
        <Link 
          to="/products"
          className="inline-flex items-center text-brown hover:text-green mb-8 transition-colors"
        >
          <ChevronLeft className="h-5 w-5 mr-1" />
          Back to Products
        </Link>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
          <div className="relative">
            <div className="aspect-square rounded-lg overflow-hidden shadow-md">
              <img 
                src={product.image} 
                alt={product.name}
                className="w-full h-full object-cover"
              />
            </div>
            {product.badge && (
              <span className="absolute top-4 right-4 bg-mustard text-brown px-4 py-2 rounded-full font-medium">
                {product.badge}
              </span>
            )}
          </div>
          
          <div className="bg-white p-8 rounded-lg shadow-md">
            <h1 className="text-3xl font-bold text-brown mb-2">{product.name}</h1>
            <p className="text-xl text-green mb-2">{product.size}</p>
            <p className="text-3xl font-bold text-brown mb-6">{product.price}</p>
            
            <div className="prose prose-brown mb-8">
              <p className="text-lg mb-6">{product.description}</p>
              
              <h3 className="text-xl font-semibold mb-4">Key Benefits</h3>
              <ul className="space-y-3">
                {product.benefits.map((benefit, index) => (
                  <li key={index} className="flex items-start">
                    <Check className="h-5 w-5 text-green mr-2 flex-shrink-0 mt-1" />
                    <span>{benefit}</span>
                  </li>
                ))}
              </ul>
            </div>
            
            <a 
              href={`https://api.whatsapp.com/send?phone=918979799769&text=${encodeURIComponent(`I want to order: ${product.name} - ${product.size} - ${product.price}`)}`}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center justify-center w-full px-6 py-4 bg-green text-white font-semibold rounded-md hover:bg-green-dark transition-colors mb-4"
            >
              <ShoppingCart className="h-6 w-6 mr-2" />
              Buy on WhatsApp
            </a>
            
            <p className="text-center text-brown/70 text-sm">
              Usually ships within 2-3 business days
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ProductDetail;





