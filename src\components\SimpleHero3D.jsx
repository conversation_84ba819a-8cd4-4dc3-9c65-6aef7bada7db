import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { ChevronDown, Play, Sparkles, Leaf } from 'lucide-react';

const SimpleHero3D = () => {
  const [activeView, setActiveView] = useState('3d-fallback');

  return (
    <section className="relative min-h-screen overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-mustard/20 via-cream to-green/20">
        {/* Floating Village Elements */}
        <div className="absolute top-20 left-10 w-4 h-4 bg-mustard/30 rounded-full village-float"></div>
        <div className="absolute top-40 right-20 w-3 h-3 bg-green/30 rounded-full village-pulse"></div>
        <div className="absolute bottom-40 left-20 w-2 h-2 bg-brown/30 rounded-full village-bounce"></div>
        <div className="absolute top-60 left-1/3 w-5 h-5 bg-mustard/20 rounded-full village-float animation-delay-150"></div>
        <div className="absolute bottom-60 right-1/3 w-3 h-3 bg-green/20 rounded-full village-pulse animation-delay-300"></div>
        
        {/* Traditional Pattern Overlay */}
        <div className="absolute inset-0 village-pattern opacity-10"></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 min-h-screen flex items-center">
        <div className="container-custom">
          <div className="text-center">
            {/* 3D-style Text */}
            <div className="mb-8">
              <h1 className="text-6xl md:text-8xl font-bold text-brown mb-4 village-glow">
                <span className="inline-block transform hover:rotate-3d transition-transform duration-500">
                  V
                </span>
                <span className="inline-block transform hover:rotate-3d transition-transform duration-500 animation-delay-150">
                  a
                </span>
                <span className="inline-block transform hover:rotate-3d transition-transform duration-500 animation-delay-300">
                  s
                </span>
                <span className="inline-block transform hover:rotate-3d transition-transform duration-500 animation-delay-150">
                  t
                </span>
                <span className="inline-block transform hover:rotate-3d transition-transform duration-500">
                  v
                </span>
                <span className="inline-block transform hover:rotate-3d transition-transform duration-500 animation-delay-300">
                  i
                </span>
                <span className="inline-block transform hover:rotate-3d transition-transform duration-500 animation-delay-150">
                  k
                </span>
              </h1>
              
              <p className="text-2xl md:text-3xl text-green font-semibold mb-2 village-shimmer">
                शुद्धता जो गांव से आयी है
              </p>
              <p className="text-xl text-brown/80">
                Pure Village Tradition Since 1950
              </p>
            </div>

            {/* 3D Oil Bottle Illustration */}
            <div className="mb-12 flex justify-center">
              <div className="relative group">
                {/* Bottle Shadow */}
                <div className="absolute top-8 left-8 w-32 h-48 bg-brown/20 rounded-lg transform rotate-6 blur-sm"></div>
                
                {/* Main Bottle */}
                <div className="relative w-32 h-48 bg-gradient-to-b from-green to-green-dark rounded-lg transform -rotate-3 group-hover:rotate-0 transition-transform duration-500 village-glow">
                  {/* Bottle Neck */}
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-2 w-8 h-6 bg-green-dark rounded-t-lg"></div>
                  
                  {/* Cap */}
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-4 w-10 h-4 bg-mustard rounded-lg"></div>
                  
                  {/* Label */}
                  <div className="absolute top-8 left-1/2 transform -translate-x-1/2 w-24 h-16 bg-cream rounded-md flex items-center justify-center">
                    <div className="text-center">
                      <div className="text-xs font-bold text-brown">VASTVIK</div>
                      <div className="text-xs text-green">Pure Oil</div>
                    </div>
                  </div>
                  
                  {/* Oil Level */}
                  <div className="absolute bottom-4 left-2 right-2 h-32 bg-mustard/80 rounded-b-lg mustard-wave"></div>
                </div>
                
                {/* Floating Particles */}
                <div className="absolute -top-4 -right-4 w-2 h-2 bg-mustard rounded-full village-float"></div>
                <div className="absolute -bottom-4 -left-4 w-1 h-1 bg-green rounded-full village-pulse"></div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-12">
              <Link 
                to="/products"
                className="px-8 py-4 bg-mustard text-brown font-bold rounded-full shadow-xl hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 village-shimmer text-lg"
              >
                🛒 Shop Village Oil
              </Link>
              
              <Link 
                to="/about"
                className="px-8 py-4 bg-green text-white font-bold rounded-full shadow-xl hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 village-shimmer text-lg"
              >
                🏘️ Our Village Story
              </Link>
              
              <button className="px-6 py-3 bg-transparent border-2 border-brown text-brown font-semibold rounded-full hover:bg-brown hover:text-white transition-all duration-300 flex items-center space-x-2">
                <Play className="h-5 w-5" />
                <span>Watch Process</span>
              </button>
            </div>

            {/* Village Stats */}
            <div className="grid grid-cols-3 gap-8 max-w-md mx-auto">
              <div className="text-center">
                <div className="text-3xl font-bold text-mustard mb-2 village-pulse">70+</div>
                <div className="text-sm text-brown/70">Years</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green mb-2 village-float">100+</div>
                <div className="text-sm text-brown/70">Families</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-brown mb-2 village-bounce">100%</div>
                <div className="text-sm text-brown/70">Pure</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Control Panel */}
      <div className="absolute top-24 right-4 z-50 space-y-2">
        <button
          onClick={() => setActiveView(activeView === '3d-fallback' ? 'traditional' : '3d-fallback')}
          className="w-12 h-12 bg-white/90 hover:bg-white rounded-full shadow-lg flex items-center justify-center transition-all duration-300 transform hover:scale-110"
          title="Switch View"
        >
          {activeView === '3d-fallback' ? (
            <Sparkles className="h-6 w-6 text-mustard" />
          ) : (
            <Leaf className="h-6 w-6 text-green" />
          )}
        </button>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20 animate-bounce">
        <ChevronDown className="h-8 w-8 text-brown drop-shadow-lg" />
      </div>

      {/* Village Smoke Effects */}
      <div className="absolute top-20 right-20 w-4 h-4 bg-white/50 rounded-full village-smoke"></div>
      <div className="absolute top-32 right-24 w-3 h-3 bg-white/40 rounded-full village-smoke animation-delay-150"></div>
      <div className="absolute top-44 right-28 w-2 h-2 bg-white/30 rounded-full village-smoke animation-delay-300"></div>

      {/* Traditional Border */}
      <div className="absolute bottom-0 left-0 right-0 h-2 village-border"></div>

      {/* Village Stats Overlay */}
      <div className="absolute bottom-20 right-8 z-50 bg-white/90 backdrop-blur-sm rounded-lg p-4 shadow-xl">
        <h3 className="font-bold text-brown mb-2">Village Heritage</h3>
        <div className="space-y-1 text-sm">
          <div className="flex justify-between">
            <span className="text-brown/70">Since:</span>
            <span className="font-semibold text-green">1950</span>
          </div>
          <div className="flex justify-between">
            <span className="text-brown/70">Families:</span>
            <span className="font-semibold text-green">100+</span>
          </div>
          <div className="flex justify-between">
            <span className="text-brown/70">Pure Oil:</span>
            <span className="font-semibold text-green">100%</span>
          </div>
        </div>
      </div>

      {/* Weather Widget */}
      <div className="absolute top-24 left-4 z-50 bg-white/90 backdrop-blur-sm rounded-lg p-4 shadow-xl">
        <div className="flex items-center space-x-2">
          <div className="text-2xl">☀️</div>
          <div>
            <div className="font-semibold text-brown">Punjab</div>
            <div className="text-sm text-brown/70">Perfect for Mustard</div>
          </div>
        </div>
      </div>

      {/* 3D Status Indicator */}
      <div className="absolute top-4 left-1/2 transform -translate-x-1/2 z-50">
        <div className="bg-white/90 backdrop-blur-sm rounded-full px-4 py-2 shadow-lg">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-brown">3D Village Experience</span>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SimpleHero3D;
