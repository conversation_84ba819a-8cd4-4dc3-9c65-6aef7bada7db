import React, { useState, useEffect } from 'react';
import { CheckCircle, X, ShoppingCart } from 'lucide-react';

const Toast = ({ message, type = 'success', isVisible, onClose, product }) => {
  useEffect(() => {
    if (isVisible) {
      const timer = setTimeout(() => {
        onClose();
      }, 4000); // Auto close after 4 seconds

      return () => clearTimeout(timer);
    }
  }, [isVisible, onClose]);

  if (!isVisible) return null;

  return (
    <div className="fixed top-4 right-4 z-50 animate-slideIn w-80 max-w-[calc(100vw-2rem)]">
      <div className={`
        w-full bg-white rounded-lg shadow-2xl border-l-4
        ${type === 'success' ? 'border-green' : 'border-red-500'}
        transform transition-all duration-300 ease-out
      `}>
        <div className="p-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              {type === 'success' ? (
                <CheckCircle className="h-6 w-6 text-green" />
              ) : (
                <ShoppingCart className="h-6 w-6 text-mustard" />
              )}
            </div>

            <div className="ml-3 w-0 flex-1">
              <div className="flex items-center justify-between">
                <p className="text-sm font-medium text-brown">
                  Product Added to Cart!
                </p>
                <button
                  onClick={onClose}
                  className="ml-4 inline-flex text-brown/60 hover:text-brown focus:outline-none"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>

              {product && (
                <div className="mt-2">
                  <p className="text-sm text-brown/80">
                    {product.name}
                  </p>
                  <div className="flex items-center justify-between mt-1">
                    <span className="text-xs text-brown/60">
                      Size: {product.size}
                    </span>
                    <span className="text-sm font-bold text-mustard">
                      {product.price}
                    </span>
                  </div>
                </div>
              )}

              <div className="mt-3 flex space-x-2">
                <button className="text-xs bg-green text-white px-3 py-1 rounded-md hover:bg-green-dark transition-colors">
                  View Cart
                </button>
                <button className="text-xs bg-mustard text-brown px-3 py-1 rounded-md hover:bg-mustard-dark transition-colors">
                  Continue Shopping
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Progress bar */}
        <div className="h-1 bg-gray-200 rounded-b-lg overflow-hidden">
          <div className="h-full bg-green animate-progress"></div>
        </div>
      </div>
    </div>
  );
};

export default Toast;
