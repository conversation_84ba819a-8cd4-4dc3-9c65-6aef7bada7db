const nodemailer = require('nodemailer');

// Create transporter
const createTransporter = () => {
  return nodemailer.createTransporter({
    host: process.env.EMAIL_HOST || 'smtp.gmail.com',
    port: process.env.EMAIL_PORT || 587,
    secure: false, // true for 465, false for other ports
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS
    },
    tls: {
      rejectUnauthorized: false
    }
  });
};

// Email templates
const emailTemplates = {
  emailVerification: (data) => ({
    subject: 'Verify Your Email - Vastavik Mustard Oil',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #8B4513; margin: 0;">🌾 Vastavik Mustard Oil</h1>
          <p style="color: #666; margin: 5px 0;">गांव से शुद्धता</p>
        </div>
        
        <div style="background: #f9f9f9; padding: 30px; border-radius: 10px; margin-bottom: 20px;">
          <h2 style="color: #333; margin-top: 0;">Welcome ${data.name}!</h2>
          <p style="color: #666; line-height: 1.6;">
            Thank you for registering with Vastavik Mustard Oil. To complete your registration 
            and start enjoying our premium cold-pressed mustard oil, please verify your email address.
          </p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${data.verificationUrl}" 
               style="background: #DAA520; color: white; padding: 15px 30px; text-decoration: none; 
                      border-radius: 5px; font-weight: bold; display: inline-block;">
              Verify Email Address
            </a>
          </div>
          
          <p style="color: #666; font-size: 14px;">
            If the button doesn't work, copy and paste this link in your browser:<br>
            <a href="${data.verificationUrl}" style="color: #DAA520;">${data.verificationUrl}</a>
          </p>
        </div>
        
        <div style="text-align: center; color: #999; font-size: 12px;">
          <p>This link will expire in 24 hours for security reasons.</p>
          <p>If you didn't create an account, please ignore this email.</p>
        </div>
      </div>
    `
  }),

  passwordReset: (data) => ({
    subject: 'Reset Your Password - Vastavik Mustard Oil',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #8B4513; margin: 0;">🌾 Vastavik Mustard Oil</h1>
          <p style="color: #666; margin: 5px 0;">गांव से शुद्धता</p>
        </div>
        
        <div style="background: #f9f9f9; padding: 30px; border-radius: 10px; margin-bottom: 20px;">
          <h2 style="color: #333; margin-top: 0;">Password Reset Request</h2>
          <p style="color: #666; line-height: 1.6;">
            Hello ${data.name},<br><br>
            We received a request to reset your password. Click the button below to create a new password:
          </p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${data.resetUrl}" 
               style="background: #DAA520; color: white; padding: 15px 30px; text-decoration: none; 
                      border-radius: 5px; font-weight: bold; display: inline-block;">
              Reset Password
            </a>
          </div>
          
          <p style="color: #666; font-size: 14px;">
            If the button doesn't work, copy and paste this link in your browser:<br>
            <a href="${data.resetUrl}" style="color: #DAA520;">${data.resetUrl}</a>
          </p>
        </div>
        
        <div style="text-align: center; color: #999; font-size: 12px;">
          <p>This link will expire in 1 hour for security reasons.</p>
          <p>If you didn't request a password reset, please ignore this email.</p>
        </div>
      </div>
    `
  }),

  contactNotification: (data) => ({
    subject: `New Contact Message: ${data.subject}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <h2 style="color: #8B4513;">New Contact Message Received</h2>
        
        <div style="background: #f9f9f9; padding: 20px; border-radius: 10px; margin: 20px 0;">
          <p><strong>Name:</strong> ${data.name}</p>
          <p><strong>Email:</strong> ${data.email}</p>
          <p><strong>Phone:</strong> ${data.phone || 'Not provided'}</p>
          <p><strong>Subject:</strong> ${data.subject}</p>
          <p><strong>Time:</strong> ${data.timestamp}</p>
        </div>
        
        <div style="background: #fff; padding: 20px; border-left: 4px solid #DAA520; margin: 20px 0;">
          <h3 style="margin-top: 0;">Message:</h3>
          <p style="line-height: 1.6;">${data.message}</p>
        </div>
      </div>
    `
  }),

  contactAutoReply: (data) => ({
    subject: 'Thank you for contacting Vastavik Mustard Oil',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #8B4513; margin: 0;">🌾 Vastavik Mustard Oil</h1>
          <p style="color: #666; margin: 5px 0;">गांव से शुद्धता</p>
        </div>
        
        <div style="background: #f9f9f9; padding: 30px; border-radius: 10px; margin-bottom: 20px;">
          <h2 style="color: #333; margin-top: 0;">Thank You for Contacting Us!</h2>
          <p style="color: #666; line-height: 1.6;">
            Dear ${data.name},<br><br>
            Thank you for reaching out to us. We have received your message regarding "${data.subject}" 
            and our team will get back to you within 24-48 hours.
          </p>
          
          <div style="background: #fff; padding: 20px; border-left: 4px solid #DAA520; margin: 20px 0;">
            <h3 style="margin-top: 0;">Your Message:</h3>
            <p style="line-height: 1.6;">${data.message}</p>
          </div>
          
          <p style="color: #666; line-height: 1.6;">
            In the meantime, feel free to explore our premium range of cold-pressed mustard oils 
            on our website. For urgent inquiries, you can also call us at +91-XXXXXXXXXX.
          </p>
        </div>
        
        <div style="text-align: center; color: #999; font-size: 12px;">
          <p>Best regards,<br>Vastavik Mustard Oil Team</p>
        </div>
      </div>
    `
  }),

  orderConfirmation: (data) => ({
    subject: `Order Confirmation - ${data.orderNumber}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #8B4513; margin: 0;">🌾 Vastavik Mustard Oil</h1>
          <p style="color: #666; margin: 5px 0;">गांव से शुद्धता</p>
        </div>
        
        <div style="background: #f9f9f9; padding: 30px; border-radius: 10px; margin-bottom: 20px;">
          <h2 style="color: #333; margin-top: 0;">Order Confirmed!</h2>
          <p style="color: #666; line-height: 1.6;">
            Dear ${data.customerName},<br><br>
            Thank you for your order! Your order has been confirmed and is being processed.
          </p>
          
          <div style="background: #fff; padding: 20px; border-radius: 5px; margin: 20px 0;">
            <h3 style="margin-top: 0; color: #8B4513;">Order Details</h3>
            <p><strong>Order Number:</strong> ${data.orderNumber}</p>
            <p><strong>Order Date:</strong> ${data.orderDate}</p>
            <p><strong>Total Amount:</strong> ₹${data.totalAmount}</p>
            <p><strong>Payment Method:</strong> ${data.paymentMethod}</p>
          </div>
          
          <p style="color: #666; line-height: 1.6;">
            We'll send you another email with tracking information once your order ships. 
            Expected delivery: ${data.expectedDelivery}
          </p>
        </div>
        
        <div style="text-align: center; color: #999; font-size: 12px;">
          <p>Thank you for choosing Vastavik Mustard Oil!</p>
        </div>
      </div>
    `
  })
};

// Send email function
const sendEmail = async (options) => {
  try {
    const transporter = createTransporter();

    let emailContent;
    
    if (options.template && emailTemplates[options.template]) {
      emailContent = emailTemplates[options.template](options.data);
    } else {
      emailContent = {
        subject: options.subject,
        html: options.html || options.text
      };
    }

    const mailOptions = {
      from: `"Vastavik Mustard Oil" <${process.env.EMAIL_FROM || process.env.EMAIL_USER}>`,
      to: options.to,
      subject: emailContent.subject,
      html: emailContent.html
    };

    const info = await transporter.sendMail(mailOptions);
    
    console.log('Email sent successfully:', info.messageId);
    return {
      success: true,
      messageId: info.messageId
    };

  } catch (error) {
    console.error('Email sending failed:', error);
    throw new Error(`Email sending failed: ${error.message}`);
  }
};

// Send bulk emails
const sendBulkEmails = async (emails) => {
  const results = [];
  
  for (const email of emails) {
    try {
      const result = await sendEmail(email);
      results.push({ ...result, to: email.to });
    } catch (error) {
      results.push({ 
        success: false, 
        error: error.message, 
        to: email.to 
      });
    }
  }
  
  return results;
};

module.exports = {
  sendEmail,
  sendBulkEmails,
  emailTemplates
};
