/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        mustard: {
          light: '#F9E076',
          DEFAULT: '#E3B505',
          dark: '#C89E04',
        },
        green: {
          light: '#5C8D2A',
          DEFAULT: '#2B580C',
          dark: '#1A3607',
        },
        brown: {
          light: '#9B6A4F',
          DEFAULT: '#784834',
          dark: '#5A3525',
        },
        cream: '#FFF8E8',
        white: '#FFFFFF',
      },
      fontFamily: {
        sans: ['Poppins', 'sans-serif'],
      },
      backgroundImage: {
        'mustard-field': "url('https://images.pexels.com/photos/3408744/pexels-photo-3408744.jpeg')",
        'village-scene': "url('https://images.pexels.com/photos/2474689/pexels-photo-2474689.jpeg')",
      },
    },
  },
  plugins: [],
};