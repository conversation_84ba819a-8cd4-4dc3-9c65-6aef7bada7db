import React from 'react';
import { MapPin, Users, Award, Leaf, Heart, Clock } from 'lucide-react';

const About = () => {
  const milestones = [
    {
      year: '1950',
      title: 'Village Roots',
      description: 'Started as a small family business in rural Punjab, using traditional wooden kolhu methods.'
    },
    {
      year: '1980',
      title: 'Community Growth',
      description: 'Expanded to support 50+ local farming families, maintaining our commitment to purity.'
    },
    {
      year: '2000',
      title: 'Modern Packaging',
      description: 'Introduced modern packaging while preserving traditional extraction methods.'
    },
    {
      year: '2020',
      title: 'Digital Presence',
      description: 'Brought our village-pure mustard oil to customers across India through online platforms.'
    }
  ];

  const values = [
    {
      icon: <Leaf className="h-12 w-12 text-green" />,
      title: 'Purity First',
      description: 'Every drop of our mustard oil is extracted using traditional methods without any chemicals or additives.'
    },
    {
      icon: <Users className="h-12 w-12 text-green" />,
      title: 'Community Support',
      description: 'We work directly with local farmers, ensuring fair prices and sustainable farming practices.'
    },
    {
      icon: <Heart className="h-12 w-12 text-green" />,
      title: 'Health & Wellness',
      description: 'Our cold-pressed oil retains all natural nutrients, promoting better health for your family.'
    },
    {
      icon: <Award className="h-12 w-12 text-green" />,
      title: 'Quality Assurance',
      description: 'Each batch is carefully tested to ensure the highest quality standards before reaching you.'
    }
  ];

  return (
    <div className="min-h-screen bg-cream pt-24">
      {/* Hero Section */}
      <section className="section-padding bg-village-scene bg-cover bg-center relative">
        <div className="absolute inset-0 bg-brown/70"></div>
        <div className="container-custom relative z-10 text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Our Village Story
          </h1>
          <p className="text-xl text-white/90 max-w-3xl mx-auto mb-8">
            From the heart of rural Punjab to your kitchen - discover the journey of 
            Vastvik Mustard Oil and our commitment to preserving traditional values.
          </p>
          <div className="flex items-center justify-center space-x-2 text-white/80">
            <MapPin className="h-5 w-5" />
            <span>Established in Village Khanna, Punjab • Since 1950</span>
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="section-padding">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="section-title text-left">Our Mission</h2>
              <p className="text-lg text-brown/80 mb-6">
                At Vastvik, we believe in preserving the authentic taste and nutritional 
                benefits of traditional mustard oil. Our mission is to bring you the purest, 
                most natural mustard oil while supporting local farming communities.
              </p>
              <p className="text-lg text-brown/80 mb-8">
                Every bottle of Vastvik Mustard Oil carries the essence of our village heritage, 
                extracted using the same wooden kolhu methods our ancestors used centuries ago.
              </p>
              <div className="grid grid-cols-2 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-green mb-2">70+</div>
                  <div className="text-brown/70">Years of Experience</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-green mb-2">100+</div>
                  <div className="text-brown/70">Farming Families</div>
                </div>
              </div>
            </div>
            <div className="relative">
              <img 
                src="https://images.pexels.com/photos/2474689/pexels-photo-2474689.jpeg" 
                alt="Village Scene" 
                className="rounded-lg shadow-xl"
              />
              <div className="absolute -bottom-6 -right-6 bg-mustard text-brown p-4 rounded-lg shadow-lg">
                <Clock className="h-8 w-8 mb-2" />
                <div className="font-semibold">Traditional Methods</div>
                <div className="text-sm">Since 1950</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="section-padding bg-white">
        <div className="container-custom">
          <h2 className="section-title">Our Core Values</h2>
          <p className="section-subtitle">
            These principles guide everything we do, from seed to bottle.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <div key={index} className="text-center group">
                <div className="mb-6 flex justify-center">
                  <div className="p-4 bg-cream rounded-full group-hover:bg-mustard/20 transition-colors">
                    {value.icon}
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-brown mb-4">{value.title}</h3>
                <p className="text-brown/70">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Timeline Section */}
      <section className="section-padding">
        <div className="container-custom">
          <h2 className="section-title">Our Journey</h2>
          <p className="section-subtitle">
            Milestones that shaped our commitment to quality and tradition.
          </p>
          
          <div className="relative">
            {/* Timeline Line */}
            <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-mustard/30 hidden md:block"></div>
            
            <div className="space-y-12">
              {milestones.map((milestone, index) => (
                <div key={index} className={`flex items-center ${
                  index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'
                }`}>
                  <div className={`w-full md:w-5/12 ${
                    index % 2 === 0 ? 'md:text-right md:pr-8' : 'md:text-left md:pl-8'
                  }`}>
                    <div className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow">
                      <div className="text-2xl font-bold text-mustard mb-2">{milestone.year}</div>
                      <h3 className="text-xl font-semibold text-brown mb-3">{milestone.title}</h3>
                      <p className="text-brown/70">{milestone.description}</p>
                    </div>
                  </div>
                  
                  {/* Timeline Dot */}
                  <div className="hidden md:flex w-2/12 justify-center">
                    <div className="w-4 h-4 bg-mustard rounded-full border-4 border-white shadow-lg"></div>
                  </div>
                  
                  <div className="hidden md:block w-5/12"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="section-padding bg-white">
        <div className="container-custom">
          <h2 className="section-title">Meet Our Family</h2>
          <p className="section-subtitle">
            The passionate people behind every bottle of Vastvik Mustard Oil.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <img 
                src="https://images.pexels.com/photos/5212320/pexels-photo-5212320.jpeg" 
                alt="Founder" 
                className="w-32 h-32 rounded-full mx-auto mb-4 object-cover"
              />
              <h3 className="text-xl font-semibold text-brown mb-2">Rajesh Singh</h3>
              <p className="text-green font-medium mb-2">Founder & Master Extractor</p>
              <p className="text-brown/70 text-sm">
                Third-generation oil extractor with 40+ years of experience in traditional methods.
              </p>
            </div>
            
            <div className="text-center">
              <img 
                src="https://images.pexels.com/photos/3414792/pexels-photo-3414792.jpeg" 
                alt="Quality Manager" 
                className="w-32 h-32 rounded-full mx-auto mb-4 object-cover"
              />
              <h3 className="text-xl font-semibold text-brown mb-2">Priya Sharma</h3>
              <p className="text-green font-medium mb-2">Quality Assurance Manager</p>
              <p className="text-brown/70 text-sm">
                Ensures every batch meets our strict quality standards before reaching customers.
              </p>
            </div>
            
            <div className="text-center">
              <img 
                src="https://images.pexels.com/photos/5212320/pexels-photo-5212320.jpeg" 
                alt="Farmer Relations" 
                className="w-32 h-32 rounded-full mx-auto mb-4 object-cover"
              />
              <h3 className="text-xl font-semibold text-brown mb-2">Harpreet Kaur</h3>
              <p className="text-green font-medium mb-2">Farmer Relations Head</p>
              <p className="text-brown/70 text-sm">
                Works directly with local farmers to ensure sustainable and fair farming practices.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section-padding bg-mustard-field bg-cover bg-center relative">
        <div className="absolute inset-0 bg-brown/60"></div>
        <div className="container-custom relative z-10 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Experience the Village Difference
          </h2>
          <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
            Join thousands of families who trust Vastvik for pure, traditional mustard oil. 
            Taste the difference that 70+ years of expertise makes.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="/products" className="btn-primary">
              Shop Our Products
            </a>
            <a href="/contact" className="px-6 py-3 bg-transparent border-2 border-white text-white 
              font-semibold rounded-md hover:bg-white/20 transition-colors duration-300">
              Contact Us
            </a>
          </div>
        </div>
      </section>
    </div>
  );
};

export default About;
