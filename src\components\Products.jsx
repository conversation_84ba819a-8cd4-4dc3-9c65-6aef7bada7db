import React, { useState } from 'react';
import { ChevronLeft, ChevronRight, ShoppingCart } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import Toast from './Toast';
import product1 from '../assets/Untitled.png';
import product2 from '../assets/Untitled1.png';
import product3 from '../assets/Untitled2.png';

const Products = () => {
  const navigate = useNavigate();

  const products = [
    {
      id: 1,
      name: "Vastavik Cold-Pressed Mustard Oil",
      size: "500ml",
      price: "₹249",
      features: [
        "Perfect for small households",
        "Fresh extraction",
        "BPA-free bottle"
      ],
      image: product1
    },
    {
      id: 2,
      name: "Vastavik Cold-Pressed Mustard Oil",
      size: "1L",
      price: "₹449",
      features: [
        "Most popular size",
        "Ideal for monthly use",
        "Premium packaging"
      ],
      image: product2
    },
    {
      id: 3,
      name: "Vastavik Cold-Pressed Mustard Oil",
      size: "5L",
      price: "₹1999",
      features: [
        "Best value pack",
        "For large families",
        "Includes pourer"
      ],
      image: product3
    }
  ];

  const [activeIndex, setActiveIndex] = useState(0);
  const [showToast, setShowToast] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);

  const nextSlide = () => {
    setActiveIndex((current) => (current === products.length - 1 ? 0 : current + 1));
  };

  const prevSlide = () => {
    setActiveIndex((current) => (current === 0 ? products.length - 1 : current - 1));
  };

  const handleBuyNow = (product) => {
    // Add to cart logic here
    console.log('Adding to cart:', product);

    // Show beautiful toast notification
    setSelectedProduct(product);
    setShowToast(true);

    // Navigate to products page after a short delay
    setTimeout(() => {
      navigate('/products');
    }, 1500);
  };

  const handleCloseToast = () => {
    setShowToast(false);
    setSelectedProduct(null);
  };

  return (
    <section id="products" className="section-padding bg-cream">
      <div className="container-custom">
        <h2 className="section-title">Our Products</h2>
        <p className="section-subtitle">
          Choose the perfect size for your household needs. All products feature our signature
          quality and purity.
        </p>

        <div className="relative overflow-visible">
          <div className="hidden md:grid md:grid-cols-1 lg:grid-cols-3 gap-8 overflow-visible">
            {products.map((product) => (
              <div key={product.id} className="card group min-h-full flex flex-col">
                <div className="h-48 mb-4 overflow-hidden rounded flex-shrink-0">
                  <img
                    src={product.image}
                    alt={product.name}
                    className="w-full h-full object-contain group-hover:scale-105 transition-transform duration-500"
                  />
                </div>
                <div className="flex-grow flex flex-col">
                  <h3 className="text-xl font-semibold mb-2">{product.name}</h3>
                  <p className="text-lg font-bold text-green">{product.size}</p>
                  <p className="text-2xl font-bold text-brown mt-2 mb-4">{product.price}</p>

                  <ul className="mb-6 space-y-2 flex-grow">
                    {product.features.map((feature, idx) => (
                      <li key={idx} className="flex items-start">
                        <span className="text-green mr-2 flex-shrink-0">✓</span>
                        <span className="text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <button
                    onClick={() => handleBuyNow(product)}
                    className="w-full btn-secondary flex items-center justify-center hover:bg-mustard hover:text-brown transition-colors duration-300 mt-auto"
                  >
                    <ShoppingCart className="h-5 w-5 mr-2" />
                    Buy Now
                  </button>
                </div>
              </div>
            ))}
          </div>

          {/* Mobile Carousel */}
          <div className="md:hidden">
            <div className="card flex flex-col min-h-full">
              <div className="h-48 mb-4 overflow-hidden rounded flex-shrink-0">
                <img
                  src={products[activeIndex].image}
                  alt={products[activeIndex].name}
                  className="w-full h-full object-contain"
                />
              </div>
              <div className="flex-grow flex flex-col">
                <h3 className="text-xl font-semibold mb-2">{products[activeIndex].name}</h3>
                <p className="text-lg font-bold text-green">{products[activeIndex].size}</p>
                <p className="text-2xl font-bold text-brown mt-2 mb-4">{products[activeIndex].price}</p>

                <ul className="mb-6 space-y-2 flex-grow">
                  {products[activeIndex].features.map((feature, idx) => (
                    <li key={idx} className="flex items-start">
                      <span className="text-green mr-2 flex-shrink-0">✓</span>
                      <span className="text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>

                <button
                  onClick={() => handleBuyNow(products[activeIndex])}
                  className="w-full btn-secondary flex items-center justify-center hover:bg-mustard hover:text-brown transition-colors duration-300 mt-auto"
                >
                  <ShoppingCart className="h-5 w-5 mr-2" />
                  Buy Now
                </button>
              </div>

              <div className="flex justify-between mt-6">
                <button
                  onClick={prevSlide}
                  className="p-2 rounded-full bg-mustard/20 text-brown hover:bg-mustard"
                >
                  <ChevronLeft className="h-6 w-6" />
                </button>
                <div className="flex space-x-2">
                  {products.map((_, idx) => (
                    <button
                      key={idx}
                      className={`w-3 h-3 rounded-full ${
                        idx === activeIndex ? 'bg-mustard' : 'bg-mustard/30'
                      }`}
                      onClick={() => setActiveIndex(idx)}
                    />
                  ))}
                </div>
                <button
                  onClick={nextSlide}
                  className="p-2 rounded-full bg-mustard/20 text-brown hover:bg-mustard"
                >
                  <ChevronRight className="h-6 w-6" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Beautiful Toast Notification */}
      <Toast
        message="Product added to cart!"
        type="success"
        isVisible={showToast}
        onClose={handleCloseToast}
        product={selectedProduct}
      />
    </section>
  );
};

export default Products;