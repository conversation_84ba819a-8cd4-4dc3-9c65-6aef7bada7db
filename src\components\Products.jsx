import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, ShoppingCart } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { useAdmin } from '../context/AdminContext';
import { productsAPI, cartAPI, notificationService, whatsappAPI } from '../services/api';
import Toast from './Toast';
import product1 from '../assets/Untitled.png';
import product2 from '../assets/Untitled1.png';
import product3 from '../assets/Untitled2.png';

const Products = () => {
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();
  const { handleNewOrder } = useAdmin();

  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeIndex, setActiveIndex] = useState(0);
  const [showToast, setShowToast] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);

  // Load products from backend
  useEffect(() => {
    loadProducts();
  }, []);

  const loadProducts = async () => {
    try {
      setLoading(true);
      const response = await productsAPI.getFeatured(3);

      // If no products from backend, use fallback data
      if (response.data.products.length === 0) {
        setProducts([
          {
            _id: '1',
            name: "Vastavik Cold-Pressed Mustard Oil",
            size: "500ml",
            price: 249,
            discountPrice: 199,
            features: [
              "Perfect for small households",
              "Fresh extraction",
              "BPA-free bottle"
            ],
            images: [{ url: product1, isPrimary: true }],
            stock: 100,
            isActive: true
          },
          {
            _id: '2',
            name: "Vastavik Cold-Pressed Mustard Oil",
            size: "1L",
            price: 449,
            discountPrice: 399,
            features: [
              "Most popular size",
              "Ideal for monthly use",
              "Premium packaging"
            ],
            images: [{ url: product2, isPrimary: true }],
            stock: 75,
            isActive: true
          },
          {
            _id: '3',
            name: "Vastavik Cold-Pressed Mustard Oil",
            size: "5L",
            price: 1999,
            discountPrice: 1799,
            features: [
              "Best value pack",
              "For large families",
              "Includes pourer"
            ],
            images: [{ url: product3, isPrimary: true }],
            stock: 50,
            isActive: true
          }
        ]);
      } else {
        setProducts(response.data.products);
      }
    } catch (error) {
      console.error('Failed to load products:', error);
      // Use fallback data on error
      setProducts([
        {
          _id: '1',
          name: "Vastavik Cold-Pressed Mustard Oil",
          size: "500ml",
          price: 249,
          discountPrice: 199,
          features: ["Perfect for small households", "Fresh extraction", "BPA-free bottle"],
          images: [{ url: product1, isPrimary: true }],
          stock: 100,
          isActive: true
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const nextSlide = () => {
    setActiveIndex((current) => (current === products.length - 1 ? 0 : current + 1));
  };

  const prevSlide = () => {
    setActiveIndex((current) => (current === 0 ? products.length - 1 : current - 1));
  };

  const handleBuyNow = async (product) => {
    try {
      if (!isAuthenticated) {
        // Redirect to login if not authenticated
        navigate('/account?tab=login');
        return;
      }

      // Add to cart via API
      await cartAPI.add(product._id, 1);

      // Show beautiful toast notification
      setSelectedProduct(product);
      setShowToast(true);

      // Send admin notification for new cart addition
      if (import.meta.env.VITE_ENABLE_NOTIFICATIONS === 'true') {
        // Send WhatsApp notification to admin
        if (import.meta.env.VITE_ENABLE_WHATSAPP_NOTIFICATIONS === 'true') {
          const message = `🛒 *Product Added to Cart!*\n\n` +
            `👤 Customer: ${user?.name}\n` +
            `📱 Phone: ${user?.phone}\n` +
            `📦 Product: ${product.name} (${product.size})\n` +
            `💰 Price: ₹${product.discountPrice || product.price}\n` +
            `⏰ Time: ${new Date().toLocaleString()}\n\n` +
            `🔗 View Cart: ${window.location.origin}/cart`;

          const whatsappUrl = `https://wa.me/${import.meta.env.VITE_ADMIN_WHATSAPP}?text=${encodeURIComponent(message)}`;
          // Don't auto-open, just log for admin reference
          console.log('WhatsApp notification ready:', whatsappUrl);
        }

        // Send browser notification to admin (if admin is logged in)
        if (user?.role === 'admin') {
          notificationService.showBrowserNotification(
            'Product Added to Cart',
            `${product.name} added by ${user.name}`
          );
        }
      }

      // Navigate to cart page after a short delay
      setTimeout(() => {
        navigate('/cart');
      }, 1500);

    } catch (error) {
      console.error('Failed to add to cart:', error);

      // Show error toast
      setSelectedProduct({
        ...product,
        error: true,
        errorMessage: error.message || 'Failed to add to cart'
      });
      setShowToast(true);
    }
  };

  const handleCloseToast = () => {
    setShowToast(false);
    setSelectedProduct(null);
  };

  return (
    <section id="products" className="section-padding bg-cream">
      <div className="container-custom">
        <h2 className="section-title">Our Products</h2>
        <p className="section-subtitle">
          Choose the perfect size for your household needs. All products feature our signature
          quality and purity.
        </p>

        <div className="relative overflow-visible">
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-mustard"></div>
            </div>
          ) : (
            <div className="hidden md:grid md:grid-cols-1 lg:grid-cols-3 gap-8 overflow-visible">
              {products.map((product) => (
                <div key={product._id} className="card group min-h-full flex flex-col">
                  <div className="h-48 mb-4 overflow-hidden rounded flex-shrink-0">
                    <img
                      src={product.images?.[0]?.url || product.image || product1}
                      alt={product.name}
                      className="w-full h-full object-contain group-hover:scale-105 transition-transform duration-500"
                    />
                  </div>
                  <div className="flex-grow flex flex-col">
                    <h3 className="text-xl font-semibold mb-2">{product.name}</h3>
                    <p className="text-lg font-bold text-green">{product.size}</p>
                    <div className="mt-2 mb-4">
                      {product.discountPrice ? (
                        <div className="flex items-center space-x-2">
                          <span className="text-2xl font-bold text-green">₹{product.discountPrice}</span>
                          <span className="text-lg text-gray-500 line-through">₹{product.price}</span>
                          <span className="text-sm bg-red-100 text-red-800 px-2 py-1 rounded">
                            {Math.round(((product.price - product.discountPrice) / product.price) * 100)}% OFF
                          </span>
                        </div>
                      ) : (
                        <span className="text-2xl font-bold text-brown">₹{product.price}</span>
                      )}
                    </div>

                    <ul className="mb-6 space-y-2 flex-grow">
                      {product.features?.map((feature, idx) => (
                        <li key={idx} className="flex items-start">
                          <span className="text-green mr-2 flex-shrink-0">✓</span>
                          <span className="text-sm">{feature}</span>
                        </li>
                      ))}
                    </ul>

                    {/* Stock indicator */}
                    {product.stock <= 10 && product.stock > 0 && (
                      <p className="text-orange-600 text-sm mb-2">Only {product.stock} left in stock!</p>
                    )}
                    {product.stock === 0 && (
                      <p className="text-red-600 text-sm mb-2">Out of stock</p>
                    )}

                    <button
                      onClick={() => handleBuyNow(product)}
                      disabled={product.stock === 0}
                      className="w-full btn-secondary flex items-center justify-center hover:bg-mustard hover:text-brown transition-colors duration-300 mt-auto disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <ShoppingCart className="h-5 w-5 mr-2" />
                      {product.stock === 0 ? 'Out of Stock' : 'Buy Now'}
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Mobile Carousel */}
          {!loading && products.length > 0 && (
            <div className="md:hidden">
              <div className="card flex flex-col min-h-full">
                <div className="h-48 mb-4 overflow-hidden rounded flex-shrink-0">
                  <img
                    src={products[activeIndex]?.images?.[0]?.url || products[activeIndex]?.image || product1}
                    alt={products[activeIndex]?.name}
                    className="w-full h-full object-contain"
                  />
                </div>
                <div className="flex-grow flex flex-col">
                  <h3 className="text-xl font-semibold mb-2">{products[activeIndex]?.name}</h3>
                  <p className="text-lg font-bold text-green">{products[activeIndex]?.size}</p>
                  <div className="mt-2 mb-4">
                    {products[activeIndex]?.discountPrice ? (
                      <div className="flex items-center space-x-2">
                        <span className="text-2xl font-bold text-green">₹{products[activeIndex]?.discountPrice}</span>
                        <span className="text-lg text-gray-500 line-through">₹{products[activeIndex]?.price}</span>
                        <span className="text-sm bg-red-100 text-red-800 px-2 py-1 rounded">
                          {Math.round(((products[activeIndex]?.price - products[activeIndex]?.discountPrice) / products[activeIndex]?.price) * 100)}% OFF
                        </span>
                      </div>
                    ) : (
                      <span className="text-2xl font-bold text-brown">₹{products[activeIndex]?.price}</span>
                    )}
                  </div>

                  <ul className="mb-6 space-y-2 flex-grow">
                    {products[activeIndex]?.features?.map((feature, idx) => (
                      <li key={idx} className="flex items-start">
                        <span className="text-green mr-2 flex-shrink-0">✓</span>
                        <span className="text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  {/* Stock indicator */}
                  {products[activeIndex]?.stock <= 10 && products[activeIndex]?.stock > 0 && (
                    <p className="text-orange-600 text-sm mb-2">Only {products[activeIndex]?.stock} left in stock!</p>
                  )}
                  {products[activeIndex]?.stock === 0 && (
                    <p className="text-red-600 text-sm mb-2">Out of stock</p>
                  )}

                  <button
                    onClick={() => handleBuyNow(products[activeIndex])}
                    disabled={products[activeIndex]?.stock === 0}
                    className="w-full btn-secondary flex items-center justify-center hover:bg-mustard hover:text-brown transition-colors duration-300 mt-auto disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <ShoppingCart className="h-5 w-5 mr-2" />
                    {products[activeIndex]?.stock === 0 ? 'Out of Stock' : 'Buy Now'}
                  </button>
                </div>

                <div className="flex justify-between mt-6">
                  <button
                    onClick={prevSlide}
                    className="p-2 rounded-full bg-mustard/20 text-brown hover:bg-mustard"
                  >
                    <ChevronLeft className="h-6 w-6" />
                  </button>
                  <div className="flex space-x-2">
                    {products.map((_, idx) => (
                      <button
                        key={idx}
                        className={`w-3 h-3 rounded-full ${
                          idx === activeIndex ? 'bg-mustard' : 'bg-mustard/30'
                        }`}
                        onClick={() => setActiveIndex(idx)}
                      />
                    ))}
                  </div>
                  <button
                    onClick={nextSlide}
                    className="p-2 rounded-full bg-mustard/20 text-brown hover:bg-mustard"
                  >
                    <ChevronRight className="h-6 w-6" />
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Beautiful Toast Notification */}
      <Toast
        message={selectedProduct?.error ? selectedProduct.errorMessage : "Product added to cart!"}
        type={selectedProduct?.error ? "error" : "success"}
        isVisible={showToast}
        onClose={handleCloseToast}
        product={selectedProduct}
      />
    </section>
  );
};

export default Products;