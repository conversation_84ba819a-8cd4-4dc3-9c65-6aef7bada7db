const notFound = (req, res, next) => {
  const error = new Error(`Not Found - ${req.originalUrl}`);
  res.status(404).json({
    success: false,
    message: `Route ${req.method} ${req.originalUrl} not found`,
    availableRoutes: {
      auth: [
        'POST /api/v1/auth/register',
        'POST /api/v1/auth/login',
        'POST /api/v1/auth/logout',
        'GET /api/v1/auth/me',
        'POST /api/v1/auth/verify-email',
        'POST /api/v1/auth/forgot-password',
        'POST /api/v1/auth/reset-password'
      ],
      products: [
        'GET /api/v1/products',
        'GET /api/v1/products/:id',
        'POST /api/v1/products (admin)',
        'PUT /api/v1/products/:id (admin)',
        'DELETE /api/v1/products/:id (admin)'
      ],
      orders: [
        'GET /api/v1/orders',
        'GET /api/v1/orders/:id',
        'POST /api/v1/orders',
        'PUT /api/v1/orders/:id',
        'DELETE /api/v1/orders/:id'
      ],
      cart: [
        'GET /api/v1/cart',
        'POST /api/v1/cart/add',
        'PUT /api/v1/cart/update',
        'DELETE /api/v1/cart/remove/:productId',
        'DELETE /api/v1/cart/clear'
      ],
      reviews: [
        'GET /api/v1/reviews/product/:productId',
        'POST /api/v1/reviews',
        'PUT /api/v1/reviews/:id',
        'DELETE /api/v1/reviews/:id'
      ],
      users: [
        'GET /api/v1/users/profile',
        'PUT /api/v1/users/profile',
        'PUT /api/v1/users/password',
        'DELETE /api/v1/users/account'
      ],
      contact: [
        'POST /api/v1/contact/message',
        'GET /api/v1/contact/messages (admin)'
      ],
      payment: [
        'POST /api/v1/payment/create-order',
        'POST /api/v1/payment/verify',
        'POST /api/v1/payment/webhook'
      ],
      admin: [
        'GET /api/v1/admin/dashboard',
        'GET /api/v1/admin/users',
        'GET /api/v1/admin/orders',
        'GET /api/v1/admin/analytics'
      ]
    },
    documentation: `${req.protocol}://${req.get('host')}/api/v1/docs`,
    health: `${req.protocol}://${req.get('host')}/health`
  });
};

module.exports = notFound;
