import React, { useEffect, useState } from 'react';
import { Leaf, Flower, Sun, Cloud } from 'lucide-react';

const VillageFloatingElements = () => {
  const [elements, setElements] = useState([]);

  useEffect(() => {
    // Create floating elements
    const createElements = () => {
      const newElements = [];
      const icons = [Leaf, Flower, Sun, Cloud];
      
      for (let i = 0; i < 8; i++) {
        const IconComponent = icons[Math.floor(Math.random() * icons.length)];
        newElements.push({
          id: i,
          Icon: IconComponent,
          x: Math.random() * 100,
          y: Math.random() * 100,
          size: Math.random() * 20 + 15,
          duration: Math.random() * 10 + 15,
          delay: Math.random() * 5,
          opacity: Math.random() * 0.3 + 0.1
        });
      }
      setElements(newElements);
    };

    createElements();
  }, []);

  return (
    <div className="fixed inset-0 pointer-events-none z-0 overflow-hidden">
      {elements.map((element) => {
        const IconComponent = element.Icon;
        return (
          <div
            key={element.id}
            className="absolute village-float"
            style={{
              left: `${element.x}%`,
              top: `${element.y}%`,
              animationDuration: `${element.duration}s`,
              animationDelay: `${element.delay}s`,
              opacity: element.opacity
            }}
          >
            <IconComponent 
              size={element.size} 
              className="text-green/20"
            />
          </div>
        );
      })}
      
      {/* Traditional patterns overlay */}
      <div className="absolute inset-0 village-pattern opacity-5"></div>
      
      {/* Subtle gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-mustard/5 via-transparent to-green/5"></div>
    </div>
  );
};

export default VillageFloatingElements;
