const errorHandler = (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;

  // Log error
  console.error('Error:', err);

  // Mongoose bad ObjectId
  if (err.name === 'CastError') {
    const message = 'Resource not found';
    error = {
      message,
      statusCode: 404
    };
  }

  // Mongoose duplicate key
  if (err.code === 11000) {
    let message = 'Duplicate field value entered';
    
    // Extract field name from error
    const field = Object.keys(err.keyValue)[0];
    if (field === 'email') {
      message = 'Email address is already registered';
    } else if (field === 'phone') {
      message = 'Phone number is already registered';
    } else if (field === 'sku') {
      message = 'Product SKU already exists';
    }
    
    error = {
      message,
      statusCode: 400
    };
  }

  // Mongoose validation error
  if (err.name === 'ValidationError') {
    const message = Object.values(err.errors).map(val => val.message).join(', ');
    error = {
      message,
      statusCode: 400
    };
  }

  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    const message = 'Invalid token';
    error = {
      message,
      statusCode: 401
    };
  }

  if (err.name === 'TokenExpiredError') {
    const message = 'Token expired';
    error = {
      message,
      statusCode: 401
    };
  }

  // Multer errors (file upload)
  if (err.code === 'LIMIT_FILE_SIZE') {
    const message = 'File size too large';
    error = {
      message,
      statusCode: 400
    };
  }

  if (err.code === 'LIMIT_UNEXPECTED_FILE') {
    const message = 'Unexpected file field';
    error = {
      message,
      statusCode: 400
    };
  }

  // Payment gateway errors
  if (err.type === 'StripeCardError') {
    const message = 'Payment failed: ' + err.message;
    error = {
      message,
      statusCode: 400
    };
  }

  if (err.error && err.error.code === 'BAD_REQUEST_ERROR') {
    const message = 'Payment failed: ' + err.error.description;
    error = {
      message,
      statusCode: 400
    };
  }

  // Rate limiting errors
  if (err.status === 429) {
    const message = 'Too many requests, please try again later';
    error = {
      message,
      statusCode: 429
    };
  }

  // Database connection errors
  if (err.name === 'MongoNetworkError') {
    const message = 'Database connection error';
    error = {
      message,
      statusCode: 503
    };
  }

  if (err.name === 'MongoTimeoutError') {
    const message = 'Database operation timeout';
    error = {
      message,
      statusCode: 503
    };
  }

  // Email service errors
  if (err.code === 'EAUTH' || err.code === 'ECONNECTION') {
    const message = 'Email service temporarily unavailable';
    error = {
      message,
      statusCode: 503
    };
  }

  // File system errors
  if (err.code === 'ENOENT') {
    const message = 'File not found';
    error = {
      message,
      statusCode: 404
    };
  }

  if (err.code === 'EACCES') {
    const message = 'Permission denied';
    error = {
      message,
      statusCode: 403
    };
  }

  // Custom application errors
  if (err.name === 'InsufficientStockError') {
    error = {
      message: err.message,
      statusCode: 400
    };
  }

  if (err.name === 'PaymentRequiredError') {
    error = {
      message: err.message,
      statusCode: 402
    };
  }

  if (err.name === 'OrderNotFoundError') {
    error = {
      message: err.message,
      statusCode: 404
    };
  }

  // Default error response
  const statusCode = error.statusCode || 500;
  const message = error.message || 'Server Error';

  // Prepare error response
  const errorResponse = {
    success: false,
    message,
    ...(process.env.NODE_ENV === 'development' && {
      stack: err.stack,
      error: err
    })
  };

  // Add additional error details for development
  if (process.env.NODE_ENV === 'development') {
    errorResponse.details = {
      name: err.name,
      code: err.code,
      statusCode: err.statusCode
    };
  }

  // Log error details for monitoring
  if (statusCode >= 500) {
    console.error('Server Error:', {
      message: err.message,
      stack: err.stack,
      url: req.originalUrl,
      method: req.method,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      user: req.user ? req.user.id : 'anonymous',
      timestamp: new Date().toISOString()
    });
  }

  res.status(statusCode).json(errorResponse);
};

module.exports = errorHandler;
