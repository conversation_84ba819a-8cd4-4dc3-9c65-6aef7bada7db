import React, { useRef, useEffect, useState } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import * as THREE from 'three';

// 3D Oil Bottle Component
function OilBottle3D({ scrollY }) {
  const bottleRef = useRef();
  const oilRef = useRef();
  const labelRef = useRef();
  
  useFrame((state) => {
    if (bottleRef.current) {
      // Calculate scroll progress (0 to 1)
      const scrollProgress = Math.min(scrollY / 1000, 1);
      
      // Bottle rotation based on scroll
      bottleRef.current.rotation.y = scrollProgress * Math.PI * 2;
      bottleRef.current.rotation.x = Math.sin(scrollProgress * Math.PI) * 0.3;
      
      // Bottle position animation
      bottleRef.current.position.y = Math.sin(scrollProgress * Math.PI * 2) * 0.5;
      bottleRef.current.position.x = Math.cos(scrollProgress * Math.PI) * 0.2;
      
      // Scale animation
      const scale = 1 + Math.sin(scrollProgress * Math.PI) * 0.2;
      bottleRef.current.scale.setScalar(scale);
    }
    
    // Oil level animation
    if (oilRef.current) {
      const oilLevel = 0.7 + Math.sin(scrollY * 0.01) * 0.3;
      oilRef.current.scale.y = oilLevel;
      oilRef.current.position.y = -0.5 + (1 - oilLevel) * 0.5;
    }
    
    // Label gentle rotation
    if (labelRef.current) {
      labelRef.current.rotation.z = Math.sin(scrollY * 0.005) * 0.1;
    }
  });

  return (
    <group ref={bottleRef} position={[0, 0, 0]}>
      {/* Bottle Body */}
      <mesh position={[0, 0, 0]} castShadow receiveShadow>
        <cylinderGeometry args={[0.6, 0.8, 2.5, 16]} />
        <meshPhysicalMaterial 
          color="#2B580C" 
          transparent 
          opacity={0.85}
          roughness={0.1}
          metalness={0.1}
          clearcoat={1}
          clearcoatRoughness={0.1}
        />
      </mesh>
      
      {/* Bottle Neck */}
      <mesh position={[0, 1.5, 0]} castShadow>
        <cylinderGeometry args={[0.25, 0.3, 0.5, 12]} />
        <meshPhysicalMaterial 
          color="#2B580C" 
          roughness={0.1}
          metalness={0.1}
        />
      </mesh>
      
      {/* Bottle Cap */}
      <mesh position={[0, 1.9, 0]} castShadow>
        <cylinderGeometry args={[0.28, 0.28, 0.2, 12]} />
        <meshPhysicalMaterial 
          color="#E3B505" 
          roughness={0.2}
          metalness={0.9}
        />
      </mesh>
      
      {/* Oil Inside Bottle */}
      <mesh ref={oilRef} position={[0, -0.5, 0]}>
        <cylinderGeometry args={[0.55, 0.75, 2, 16]} />
        <meshPhysicalMaterial 
          color="#FFD700" 
          transparent 
          opacity={0.9}
          roughness={0}
          metalness={0}
          transmission={0.8}
          thickness={0.5}
        />
      </mesh>
      
      {/* Label */}
      <mesh ref={labelRef} position={[0, 0.2, 0.61]}>
        <planeGeometry args={[1, 1.2]} />
        <meshStandardMaterial color="#FFF8E8" />
      </mesh>
      
      {/* Vastvik Text */}
      <mesh position={[0, 0.5, 0.62]}>
        <planeGeometry args={[0.7, 0.15]} />
        <meshStandardMaterial color="#2B580C" />
      </mesh>
      
      {/* Pure Oil Text */}
      <mesh position={[0, 0.1, 0.62]}>
        <planeGeometry args={[0.5, 0.1]} />
        <meshStandardMaterial color="#E3B505" />
      </mesh>
      
      {/* Hindi Text */}
      <mesh position={[0, -0.2, 0.62]}>
        <planeGeometry args={[0.8, 0.1]} />
        <meshStandardMaterial color="#2B580C" />
      </mesh>
    </group>
  );
}

// Floating Particles
function FloatingParticles({ scrollY }) {
  const particlesRef = useRef();
  
  const particlePositions = React.useMemo(() => {
    const positions = new Float32Array(50 * 3);
    for (let i = 0; i < 50; i++) {
      positions[i * 3] = (Math.random() - 0.5) * 8;
      positions[i * 3 + 1] = (Math.random() - 0.5) * 8;
      positions[i * 3 + 2] = (Math.random() - 0.5) * 8;
    }
    return positions;
  }, []);
  
  useFrame((state) => {
    if (particlesRef.current) {
      const scrollProgress = scrollY / 1000;
      particlesRef.current.rotation.y = scrollProgress * Math.PI;
      particlesRef.current.rotation.x = Math.sin(scrollProgress * Math.PI) * 0.2;
    }
  });
  
  return (
    <points ref={particlesRef}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={50}
          array={particlePositions}
          itemSize={3}
        />
      </bufferGeometry>
      <pointsMaterial size={0.03} color="#E3B505" transparent opacity={0.8} />
    </points>
  );
}

// Main 3D Scene
function BottleScene({ scrollY }) {
  return (
    <>
      {/* Lighting */}
      <ambientLight intensity={0.4} />
      <directionalLight 
        position={[5, 5, 5]} 
        intensity={1} 
        castShadow 
        shadow-mapSize-width={1024}
        shadow-mapSize-height={1024}
      />
      <pointLight position={[-5, -5, -5]} intensity={0.3} color="#E3B505" />
      <spotLight 
        position={[0, 8, 0]} 
        intensity={0.6} 
        angle={0.3} 
        penumbra={1} 
        castShadow 
      />
      
      {/* 3D Oil Bottle */}
      <OilBottle3D scrollY={scrollY} />
      
      {/* Floating Particles */}
      <FloatingParticles scrollY={scrollY} />
      
      {/* Ground */}
      <mesh position={[0, -2, 0]} rotation={[-Math.PI / 2, 0, 0]} receiveShadow>
        <planeGeometry args={[10, 10]} />
        <meshStandardMaterial color="#90EE90" transparent opacity={0.2} />
      </mesh>
    </>
  );
}

// Main Component
const SimpleScrollBottle3D = () => {
  const [scrollY, setScrollY] = useState(0);
  
  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };
    
    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);
  
  return (
    <div className="fixed top-0 right-0 w-1/2 h-screen pointer-events-none z-10">
      <Canvas
        camera={{ position: [2, 1, 4], fov: 50 }}
        shadows
        className="w-full h-full"
      >
        <BottleScene scrollY={scrollY} />
      </Canvas>
      
      {/* Scroll Progress Indicator */}
      <div className="absolute bottom-8 right-8 bg-white/90 backdrop-blur-sm rounded-lg p-4 shadow-xl pointer-events-auto">
        <div className="text-center">
          <div className="text-sm font-medium text-brown mb-2">3D Bottle</div>
          <div className="w-16 h-2 bg-cream rounded-full overflow-hidden">
            <div 
              className="h-full bg-gradient-to-r from-mustard to-green transition-all duration-300"
              style={{ width: `${Math.min((scrollY / 1000) * 100, 100)}%` }}
            ></div>
          </div>
          <div className="text-xs text-brown/70 mt-1">Scroll to animate</div>
        </div>
      </div>
      
      {/* Animation Info */}
      <div className="absolute top-8 right-8 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-xl pointer-events-auto">
        <div className="text-xs text-brown">
          <div className="font-semibold mb-1">🎮 3D Effects</div>
          <div>🔄 Bottle rotates</div>
          <div>🌊 Oil level changes</div>
          <div>✨ Particles float</div>
          <div>📏 Scale animation</div>
        </div>
      </div>
      
      {/* Scroll Value Display */}
      <div className="absolute top-1/2 right-4 bg-black/50 text-white px-2 py-1 rounded text-xs">
        Scroll: {Math.round(scrollY)}px
      </div>
    </div>
  );
};

export default SimpleScrollBottle3D;
