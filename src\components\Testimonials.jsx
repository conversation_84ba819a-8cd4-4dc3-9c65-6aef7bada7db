import React, { useState } from 'react';
import { ChevronLeft, ChevronRight, Star } from 'lucide-react';

const Testimonials = () => {
  const testimonials = [
    {
      id: 1,
      name: "<PERSON><PERSON>",
      location: "Farmer, Uttar Pradesh",
      image: "https://images.pexels.com/photos/5212320/pexels-photo-5212320.jpeg",
      quote: "I've been using Vastavik oil for generations in my family. The taste is exactly how mustard oil should be - sharp, authentic and reminds me of my childhood."
    },
    {
      id: 2,
      name: "<PERSON>",
      location: "Homemaker, Rajasthan",
      image: "https://images.pexels.com/photos/3414792/pexels-photo-3414792.jpeg",
      quote: "There's nothing like the aroma of Vastavik mustard oil while cooking. My dishes have that traditional flavor that my family loves. I won't use anything else."
    },
    {
      id: 3,
      name: "<PERSON>",
      location: "Village Elder, Haryana",
      image: "https://images.pexels.com/photos/2774546/pexels-photo-2774546.jpeg",
      quote: "In our village, we know real quality. Vastvik doesn't just sell oil, they preserve our tradition. The kolhu-pressed method makes all the difference."
    }
  ];

  const [activeIndex, setActiveIndex] = useState(0);

  const nextSlide = () => {
    setActiveIndex((current) => (current === testimonials.length - 1 ? 0 : current + 1));
  };

  const prevSlide = () => {
    setActiveIndex((current) => (current === 0 ? testimonials.length - 1 : current - 1));
  };

  return (
    <section id="testimonials" className="section-padding bg-mustard/10">
      <div className="container-custom">
        <h2 className="section-title">What Our Customers Say</h2>
        <p className="section-subtitle">
          Hear from the people who have made Vastvik Mustard Oil a part of their daily lives.
        </p>

        {/* Desktop View */}
        <div className="hidden md:grid md:grid-cols-3 gap-8">
          {testimonials.map((testimonial) => (
            <div key={testimonial.id} className="card relative">
              <div className="absolute -top-4 -right-4 flex">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 fill-mustard text-mustard" />
                ))}
              </div>

              <div className="flex items-center space-x-4 mb-4">
                <img
                  src={testimonial.image}
                  alt={testimonial.name}
                  className="w-16 h-16 rounded-full object-cover"
                />
                <div>
                  <h3 className="font-semibold">{testimonial.name}</h3>
                  <p className="text-sm text-brown/70">{testimonial.location}</p>
                </div>
              </div>

              <p className="italic text-brown/80">"{testimonial.quote}"</p>
            </div>
          ))}
        </div>

        {/* Mobile Carousel */}
        <div className="md:hidden relative">
          <div className="card">
            <div className="absolute -top-4 -right-4 flex">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className="h-5 w-5 fill-mustard text-mustard" />
              ))}
            </div>

            <div className="flex items-center space-x-4 mb-4">
              <img
                src={testimonials[activeIndex].image}
                alt={testimonials[activeIndex].name}
                className="w-16 h-16 rounded-full object-cover"
              />
              <div>
                <h3 className="font-semibold">{testimonials[activeIndex].name}</h3>
                <p className="text-sm text-brown/70">{testimonials[activeIndex].location}</p>
              </div>
            </div>

            <p className="italic text-brown/80">"{testimonials[activeIndex].quote}"</p>

            <div className="flex justify-between mt-6">
              <button
                onClick={prevSlide}
                className="p-2 rounded-full bg-mustard/20 text-brown hover:bg-mustard"
              >
                <ChevronLeft className="h-6 w-6" />
              </button>
              <div className="flex space-x-2">
                {testimonials.map((_, idx) => (
                  <button
                    key={idx}
                    className={`w-3 h-3 rounded-full ${
                      idx === activeIndex ? 'bg-mustard' : 'bg-mustard/30'
                    }`}
                    onClick={() => setActiveIndex(idx)}
                  />
                ))}
              </div>
              <button
                onClick={nextSlide}
                className="p-2 rounded-full bg-mustard/20 text-brown hover:bg-mustard"
              >
                <ChevronRight className="h-6 w-6" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;