const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Import models
const User = require('../models/User');
const Product = require('../models/Product');

// Connect to database
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/vastavik_mustard_oil', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Sample data
const sampleUsers = [
  {
    name: 'Admin User',
    email: '<EMAIL>',
    phone: '9876543210',
    password: 'admin123',
    role: 'admin',
    isEmailVerified: true,
    isPhoneVerified: true,
    address: {
      street: '123 Admin Street',
      city: 'Delhi',
      state: 'Delhi',
      pincode: '110001',
      country: 'India'
    }
  },
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '9876543211',
    password: 'user123',
    role: 'user',
    isEmailVerified: true,
    address: {
      street: '456 User Lane',
      city: 'Mumbai',
      state: 'Maharashtra',
      pincode: '400001',
      country: 'India'
    }
  },
  {
    name: 'Jane Smith',
    email: '<EMAIL>',
    phone: '9876543212',
    password: 'user123',
    role: 'user',
    isEmailVerified: true,
    address: {
      street: '789 Customer Road',
      city: 'Bangalore',
      state: 'Karnataka',
      pincode: '560001',
      country: 'India'
    }
  }
];

const sampleProducts = [
  {
    name: 'Vastavik Cold-Pressed Mustard Oil',
    description: 'Premium quality cold-pressed mustard oil made from finest mustard seeds. Rich in omega-3 fatty acids and natural antioxidants. Perfect for cooking, massage, and hair care.',
    shortDescription: 'Premium cold-pressed mustard oil for cooking and wellness',
    price: 249,
    discountPrice: 199,
    size: '500ml',
    weight: { value: 500, unit: 'ml' },
    category: 'cold-pressed',
    images: [
      {
        public_id: 'vastavik_500ml_1',
        url: '/images/products/vastavik-500ml-1.jpg',
        alt: 'Vastavik 500ml Mustard Oil Front View',
        isPrimary: true
      }
    ],
    features: [
      'Cold-pressed extraction method',
      'Rich in Omega-3 fatty acids',
      'No artificial preservatives',
      'Traditional wooden pressing',
      'BPA-free packaging'
    ],
    ingredients: [
      { name: 'Mustard Seeds', percentage: 100, description: 'Pure mustard seeds' }
    ],
    nutritionalInfo: {
      calories: 884,
      fat: 100,
      saturatedFat: 12,
      cholesterol: 0,
      sodium: 0,
      vitaminE: 17.2,
      omega3: 5.9,
      omega6: 15.3
    },
    stock: 100,
    lowStockThreshold: 10,
    sku: 'VMO500ML001',
    tags: ['cold-pressed', 'organic', 'traditional', 'healthy'],
    seoData: {
      metaTitle: 'Vastavik Cold-Pressed Mustard Oil 500ml - Premium Quality',
      metaDescription: 'Buy premium cold-pressed mustard oil online. Rich in omega-3, perfect for cooking and wellness. Free delivery available.',
      keywords: ['mustard oil', 'cold-pressed', 'organic', 'cooking oil'],
      slug: 'vastavik-cold-pressed-mustard-oil-500ml'
    },
    certifications: [
      {
        name: 'fssai',
        number: 'FSSAI-12345678901234',
        issuedBy: 'Food Safety and Standards Authority of India',
        validUntil: new Date('2025-12-31')
      }
    ],
    shipping: {
      weight: 0.6,
      dimensions: { length: 15, width: 8, height: 25 },
      fragile: true,
      freeShipping: false,
      shippingCost: 50
    },
    isActive: true,
    isFeatured: true,
    isNewArrival: true,
    isBestSeller: true
  },
  {
    name: 'Vastavik Cold-Pressed Mustard Oil',
    description: 'Family pack of premium cold-pressed mustard oil. Ideal for regular cooking needs. Made using traditional methods for authentic taste and maximum nutrition.',
    shortDescription: 'Family pack cold-pressed mustard oil for daily cooking',
    price: 449,
    discountPrice: 399,
    size: '1L',
    weight: { value: 1, unit: 'L' },
    category: 'cold-pressed',
    images: [
      {
        public_id: 'vastavik_1l_1',
        url: '/images/products/vastavik-1l-1.jpg',
        alt: 'Vastavik 1L Mustard Oil Front View',
        isPrimary: true
      }
    ],
    features: [
      'Perfect for family use',
      'Traditional extraction method',
      'Rich aroma and taste',
      'Premium packaging',
      'Value for money'
    ],
    ingredients: [
      { name: 'Mustard Seeds', percentage: 100, description: 'Pure mustard seeds' }
    ],
    nutritionalInfo: {
      calories: 884,
      fat: 100,
      saturatedFat: 12,
      cholesterol: 0,
      sodium: 0,
      vitaminE: 17.2,
      omega3: 5.9,
      omega6: 15.3
    },
    stock: 75,
    lowStockThreshold: 15,
    sku: 'VMO1L001',
    tags: ['cold-pressed', 'family-pack', 'traditional', 'value'],
    seoData: {
      metaTitle: 'Vastavik Cold-Pressed Mustard Oil 1L - Family Pack',
      metaDescription: 'Premium 1L mustard oil for families. Cold-pressed, traditional method. Best value for daily cooking needs.',
      keywords: ['mustard oil 1l', 'family pack', 'cold-pressed', 'cooking'],
      slug: 'vastavik-cold-pressed-mustard-oil-1l'
    },
    certifications: [
      {
        name: 'fssai',
        number: 'FSSAI-12345678901234',
        issuedBy: 'Food Safety and Standards Authority of India',
        validUntil: new Date('2025-12-31')
      }
    ],
    shipping: {
      weight: 1.1,
      dimensions: { length: 18, width: 10, height: 30 },
      fragile: true,
      freeShipping: false,
      shippingCost: 60
    },
    isActive: true,
    isFeatured: true,
    isBestSeller: true
  },
  {
    name: 'Vastavik Cold-Pressed Mustard Oil',
    description: 'Bulk pack for large families and commercial use. Premium quality cold-pressed mustard oil with authentic taste. Best value for money with free delivery.',
    shortDescription: 'Bulk pack cold-pressed mustard oil with free delivery',
    price: 1999,
    discountPrice: 1799,
    size: '5L',
    weight: { value: 5, unit: 'L' },
    category: 'cold-pressed',
    images: [
      {
        public_id: 'vastavik_5l_1',
        url: '/images/products/vastavik-5l-1.jpg',
        alt: 'Vastavik 5L Mustard Oil Front View',
        isPrimary: true
      }
    ],
    features: [
      'Best value pack',
      'Free home delivery',
      'Includes convenient pourer',
      'Perfect for large families',
      'Bulk discount pricing'
    ],
    ingredients: [
      { name: 'Mustard Seeds', percentage: 100, description: 'Pure mustard seeds' }
    ],
    nutritionalInfo: {
      calories: 884,
      fat: 100,
      saturatedFat: 12,
      cholesterol: 0,
      sodium: 0,
      vitaminE: 17.2,
      omega3: 5.9,
      omega6: 15.3
    },
    stock: 50,
    lowStockThreshold: 5,
    sku: 'VMO5L001',
    tags: ['cold-pressed', 'bulk', 'value-pack', 'free-delivery'],
    seoData: {
      metaTitle: 'Vastavik Cold-Pressed Mustard Oil 5L - Best Value Pack',
      metaDescription: 'Buy 5L mustard oil bulk pack online. Best value for large families. Free delivery included. Premium cold-pressed quality.',
      keywords: ['mustard oil 5l', 'bulk pack', 'free delivery', 'value pack'],
      slug: 'vastavik-cold-pressed-mustard-oil-5l'
    },
    certifications: [
      {
        name: 'fssai',
        number: 'FSSAI-12345678901234',
        issuedBy: 'Food Safety and Standards Authority of India',
        validUntil: new Date('2025-12-31')
      }
    ],
    shipping: {
      weight: 5.5,
      dimensions: { length: 25, width: 15, height: 35 },
      fragile: true,
      freeShipping: true,
      shippingCost: 0
    },
    isActive: true,
    isFeatured: true,
    isBestSeller: false
  }
];

// Seed function
const seedData = async () => {
  try {
    console.log('🌱 Starting data seeding...');

    // Clear existing data
    await User.deleteMany({});
    await Product.deleteMany({});
    console.log('🗑️  Cleared existing data');

    // Create admin user first
    const adminUser = await User.create(sampleUsers[0]);
    console.log('👤 Created admin user');

    // Create other users
    const users = await User.create(sampleUsers.slice(1));
    console.log(`👥 Created ${users.length} regular users`);

    // Add createdBy field to products
    const productsWithCreator = sampleProducts.map(product => ({
      ...product,
      createdBy: adminUser._id
    }));

    // Create products
    const products = await Product.create(productsWithCreator);
    console.log(`📦 Created ${products.length} products`);

    console.log('✅ Data seeding completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`   Users: ${sampleUsers.length} (1 admin, ${sampleUsers.length - 1} regular)`);
    console.log(`   Products: ${products.length}`);
    console.log('\n🔐 Admin Credentials:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: admin123');
    console.log('\n🔐 Test User Credentials:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: user123');

  } catch (error) {
    console.error('❌ Error seeding data:', error);
  } finally {
    mongoose.connection.close();
    console.log('\n👋 Database connection closed');
  }
};

// Run seeding
connectDB().then(() => {
  seedData();
});

module.exports = { seedData };
